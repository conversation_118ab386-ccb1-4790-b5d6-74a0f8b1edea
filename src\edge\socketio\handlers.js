const { logger } = require('../../utils/logger');
const { v4: uuidv4 } = require('uuid');
const { publishToRabbit } = require('../../config/queue.config');

const createHandlers = (io, container) => {
  const activeSubscriptions = new Map();

  return {
    // Chat-related handlers

    async handleNotificationRead(socket, data) {
      try {
        const { notificationId } = data;

        // Mark notification as read using cache
        const { cache } = require('../../config/cache.config');
        await cache.set(
          `notification:read:${notificationId}`,
          { userId: socket.userId, readAt: new Date() },
          86400 // 24 hours
        );

        logger.debug(`Notification ${notificationId} marked as read`);
      } catch (error) {
        logger.error(`Notification read error: ${error}`);
        throw error;
      }
    },

    async handleMarkAllNotificationsRead(socket) {
      try {
        // Mark all notifications as read for user
        await publishToRabbit('events', 'notification.mark-all-read', {
          userId: socket.userId,
          timestamp: new Date()
        });

        logger.debug(`All notifications marked as read for user ${socket.userId}`);
      } catch (error) {
        logger.error(`Mark all notifications read error: ${error}`);
        throw error;
      }
    },

    async handlePublicOrderTracking(socket, data) {
      try {
        const { trackingNumber } = data;

        if (!trackingNumber) {
          throw new Error('Tracking number is required');
        }

        // Mock tracking info
        const trackingInfo = {
          trackingNumber,
          status: 'in_transit',
          currentLocation: 'Distribution Center',
          estimatedDelivery: new Date(Date.now() + 24 * 60 * 60 * 1000),
          history: []
        };

        await publishToRabbit('events', 'order.tracking', {
          trackingNumber,
          trackingInfo,
          userId: socket.userId || null
        });

        return trackingInfo;
      } catch (error) {
        logger.error(`Order tracking error: ${error}`);
        throw error;
      }
    },

    handleDisconnect(socket) {
      // Clean up subscriptions
      activeSubscriptions.delete(socket.id);

      // Update user status to offline
      if (socket.userId) {
        const { cache } = require('../../config/cache.config');
        cache.set(
          `user:status:${socket.userId}`,
          { status: 'offline', lastSeen: new Date() },
          300
        ).catch(err => logger.error('Failed to update user status:', err));
      }
    },

    // Chat-related handlers
    async handleChatJoin(socket, data) {
      try {
        const { conversationId } = data;

        if (!conversationId) {
          throw new Error('conversationId is required');
        }

        // Create conversation in database if it doesn't exist
        try {
          const { getModels } = require('../../models');
          const writeModels = getModels('write');
          const { ChatConversation } = writeModels;

          await ChatConversation.findOrCreate({
            where: { id: conversationId },
            defaults: {
              id: conversationId,
              user1Id: socket.userId,
              user2Id: socket.userId, // Will be updated when second user joins
              lastActivity: new Date(),
              isActive: true
            }
          });

          logger.info(`📝 Conversation ensured in database: ${conversationId}`);
        } catch (dbError) {
          logger.warn(`Failed to create conversation in database: ${dbError.message}`);
        }

        // Join the conversation room
        socket.join(conversationId);

        logger.info(`User ${socket.userId} joined chat: ${conversationId}`);

        // Send join success
        socket.emit('chat:joined', {
          conversationId,
          joinedAt: new Date()
        });

        // Use io to send activity notification
        io.of('/notifications').to(`user:${socket.userId}`).emit('chat:activity', {
          type: 'joined_chat',
          conversationId,
          timestamp: new Date()
        });

        // Notify other participants
        socket.to(conversationId).emit('chat:user-joined', {
          userId: socket.userId,
          conversationId,
          joinedAt: new Date()
        });

      } catch (error) {
        logger.error(`Chat join error: ${error}`);
        socket.emit('error', { event: 'chat:join', message: error.message });
      }
    }, async handleChatLeave(socket, data) {
      try {
        const { conversationId } = data;

        if (!conversationId) {
          throw new Error('conversationId is required');
        }

        // Leave the conversation room
        socket.leave(conversationId);

        logger.info(`User ${socket.userId} left chat: ${conversationId}`);

        // Send leave success
        socket.emit('chat:left', {
          conversationId,
          leftAt: new Date()
        });

        // Notify other participants
        socket.to(conversationId).emit('chat:user-left', {
          userId: socket.userId,
          conversationId,
          leftAt: new Date()
        });

      } catch (error) {
        logger.error(`Chat leave error: ${error}`);
        socket.emit('error', { event: 'chat:leave', message: error.message });
      }
    },

    async handleMessageSend(socket, data) {
      try {
        const { conversationId, content, recipientId, type = 'text' } = data;
        console.log("ddd:", data)
        // Validate required fields
        if (!conversationId || !content || !recipientId) {
          throw new Error('conversationId, content, and recipientId are required');
        }

        if (!content || content.trim().length === 0) {
          throw new Error('Message cannot be empty');
        }

        // Use available userService to validate recipient
        const userService = container.resolve('userService');
        try {
          await userService.getUserById(recipientId);
        } catch (err) {
          logger.warn(`User validation failed: ${err.message}`);
        }

        // Store message
        const messageData = {
          id: uuidv4(), // Use proper UUID for database compatibility
          conversationId,
          senderId: socket.userId,
          recipientId,
          content,
          type,
          timestamp: new Date(),
          isRead: false
        };

        // Save to database using chat models
        try {
          // Get write models specifically for database operations
          const { getModels } = require('../../models');
          const writeModels = getModels('write');
          const { ChatMessage, ChatConversation } = writeModels;

          logger.info(`📋 Available write models: ${Object.keys(writeModels).join(', ')}`);
          logger.info(`🔍 Attempting to save message to database...`);
          logger.info(`✅ ChatMessage model available: ${!!ChatMessage}`);
          logger.info(`✅ ChatConversation model available: ${!!ChatConversation}`);

          // Create or update conversation first
          const [conversation, conversationCreated] = await ChatConversation.findOrCreate({
            where: { id: conversationId },
            defaults: {
              id: conversationId,
              user1Id: socket.userId,
              user2Id: recipientId,
              lastActivity: new Date(),
              isActive: true
            }
          });

          logger.info(`💬 Conversation ${conversationCreated ? 'created' : 'found'}: ${conversationId}`);

          // Save message to database
          const savedMessage = await ChatMessage.create({
            id: messageData.id,
            conversationId,
            senderId: socket.userId,
            recipientId,
            content,
            type,
            isRead: false,
            createdAt: new Date()
          });

          logger.info(`✅ Message saved to database successfully: ${messageData.id}`);
          logger.info(`📊 Saved message data:`, {
            id: savedMessage.id,
            conversationId: savedMessage.conversationId,
            senderId: savedMessage.senderId,
            content: savedMessage.content.substring(0, 50) + '...'
          });

          // Update conversation last activity
          await ChatConversation.update(
            { lastActivity: new Date() },
            { where: { id: conversationId } }
          );

          logger.info(`🔄 Conversation last activity updated: ${conversationId}`);

          // Verify data was saved by querying back
          const messageCount = await ChatMessage.count({ where: { conversationId } });
          const conversationCount = await ChatConversation.count({ where: { id: conversationId } });

          logger.info(`📈 Database verification - Messages in conversation: ${messageCount}, Conversations: ${conversationCount}`);

        } catch (dbError) {
          logger.error(`❌ Database save failed: ${dbError.message}`);
          logger.error(`🔍 Full error:`, dbError);
        }

        // Also save to queue for persistence
        await publishToRabbit('events', 'chat.message', messageData);

        // 1. Send to sender (acknowledgment with message data)
        socket.emit('message:sent', messageData);

        // 2. Send to receiver in same conversation room
        socket.to(conversationId).emit('message:receive', messageData);

        // 3. Also broadcast to conversation room (includes both users if they joined)
        io.of('/chat').to(conversationId).emit('message:receive', messageData);

        // 4. Send direct notification to recipient
        io.of('/notifications').to(`user:${recipientId}`).emit('notification:new', {
          type: 'message',
          senderId: socket.userId,
          conversationId,
          content: content.substring(0, 100) + (content.length > 100 ? '...' : ''),
          timestamp: new Date()
        }); return messageData;
      } catch (error) {
        logger.error(`Message send error: ${error}`);
        socket.emit('error', { event: 'message:send', message: error.message });
      }
    },

    handleTypingStart(socket, data) {
      try {
        const { conversationId } = data;

        if (!conversationId) {
          throw new Error('conversationId is required');
        }

        // Broadcast to conversation room
        socket.to(conversationId).emit('typing:started', {
          userId: socket.userId,
          conversationId,
          startedAt: new Date()
        });

        logger.debug(`User ${socket.userId} started typing in ${conversationId}`);
      } catch (error) {
        logger.error(`Typing start error: ${error}`);
      }
    },

    handleTypingStop(socket, data) {
      try {
        const { conversationId } = data;

        if (!conversationId) {
          throw new Error('conversationId is required');
        }

        // Broadcast to conversation room
        socket.to(conversationId).emit('typing:stopped', {
          userId: socket.userId,
          conversationId,
          stoppedAt: new Date()
        });

        logger.debug(`User ${socket.userId} stopped typing in ${conversationId}`);
      } catch (error) {
        logger.error(`Typing stop error: ${error}`);
      }
    },

    async handleMessageMarkRead(socket, data) {
      try {
        const { messageId, conversationId } = data;

        if (!messageId) {
          throw new Error('messageId is required');
        }

        // Mark message as read in database/cache
        const { cache } = require('../../config/cache.config');
        await cache.set(
          `message:read:${messageId}`,
          {
            userId: socket.userId,
            readAt: new Date(),
            conversationId
          },
          86400 // 24 hours
        );

        // Publish to queue for persistence
        await publishToRabbit('events', 'message.mark-read', {
          messageId,
          conversationId,
          userId: socket.userId,
          readAt: new Date()
        });

        // Broadcast to conversation room
        if (conversationId) {
          io.of('/chat').to(conversationId).emit('message:read', {
            messageId,
            conversationId,
            readBy: socket.userId,
            readAt: new Date()
          });
        }

        // Send acknowledgment
        socket.emit('message:marked-read', { messageId });

        logger.debug(`Message ${messageId} marked as read by user ${socket.userId}`);

      } catch (error) {
        logger.error(`Message mark read error: ${error}`);
        socket.emit('error', { event: 'message:mark-read', message: error.message });
      }
    },

    async handleGetConversation(socket, data) {
      try {
        const { conversationId, page = 1, limit = 50 } = data;

        if (!conversationId) {
          throw new Error('conversationId is required');
        }

        // Get conversation with messages using database models
        try {
          const { getModels } = require('../../models');
          const readModels = getModels('read');
          const { ChatConversation, ChatMessage, User } = readModels;

          // Get conversation details
          const conversation = await ChatConversation.findOne({
            where: { id: conversationId },
            include: [
              {
                model: User,
                as: 'user1',
                attributes: ['id', 'fullName', 'email', 'ProfileImageURL']
              },
              {
                model: User,
                as: 'user2',
                attributes: ['id', 'fullName', 'email', 'ProfileImageURL']
              }
            ]
          });

          if (!conversation) {
            throw new Error('Conversation not found');
          }

          // Check if user is participant
          const isParticipant = conversation.user1Id === socket.userId || conversation.user2Id === socket.userId;
          if (!isParticipant) {
            throw new Error('Unauthorized access to conversation');
          }

          // Get messages with pagination
          const offset = (page - 1) * limit;
          const messages = await ChatMessage.findAll({
            where: { conversationId },
            include: [
              {
                model: User,
                as: 'sender',
                attributes: ['id', 'fullName', 'ProfileImageURL']
              },
              {
                model: User,
                as: 'recipient',
                attributes: ['id', 'fullName', 'ProfileImageURL']
              }
            ],
            order: [['createdAt', 'DESC']],
            limit: parseInt(limit),
            offset: parseInt(offset)
          });

          // Get total message count
          const totalMessages = await ChatMessage.count({
            where: { conversationId }
          });

          const conversationData = {
            id: conversation.id,
            user1: conversation.user1,
            user2: conversation.user2,
            propertyId: conversation.propertyId,
            propertyTitle: conversation.propertyTitle,
            lastActivity: conversation.lastActivity,
            isActive: conversation.isActive,
            messages: messages.reverse(), // Reverse to show oldest first
            pagination: {
              page: parseInt(page),
              limit: parseInt(limit),
              total: totalMessages,
              hasMore: offset + messages.length < totalMessages
            }
          };

          socket.emit('conversation:data', {
            success: true,
            conversation: conversationData
          });

          logger.info(`📖 Sent conversation data for ${conversationId} to user ${socket.userId}`);

        } catch (dbError) {
          logger.error(`Database error in handleGetConversation: ${dbError.message}`);
          throw new Error('Failed to fetch conversation from database');
        }

      } catch (error) {
        logger.error(`Get conversation error: ${error}`);
        socket.emit('error', { event: 'conversation:get', message: error.message });
      }
    },

    async handleListConversations(socket, data) {
      try {
        const { limit = 20, offset = 0 } = data || {};

        // Get user conversations using database models
        try {
          const { getModels } = require('../../models');
          const readModels = getModels('read');
          const { ChatConversation, User } = readModels;

          const conversations = await ChatConversation.findAll({
            where: {
              [require('sequelize').Op.or]: [
                { user1Id: socket.userId },
                { user2Id: socket.userId }
              ],
              isActive: true
            },
            include: [
              {
                model: User,
                as: 'user1',
                attributes: ['id', 'fullName', 'email', 'ProfileImageURL']
              },
              {
                model: User,
                as: 'user2',
                attributes: ['id', 'fullName', 'email', 'ProfileImageURL']
              }
            ],
            order: [['lastActivity', 'DESC']],
            limit: parseInt(limit),
            offset: parseInt(offset)
          });

          // Get total conversation count
          const totalConversations = await ChatConversation.count({
            where: {
              [require('sequelize').Op.or]: [
                { user1Id: socket.userId },
                { user2Id: socket.userId }
              ],
              isActive: true
            }
          });

          // Format conversations for frontend
          const formattedConversations = conversations.map(conv => {
            // Determine other participant
            const otherParticipant = conv.user1Id === socket.userId ? conv.user2 : conv.user1;

            return {
              id: conv.id,
              otherParticipant: {
                id: otherParticipant.id,
                name: otherParticipant.fullName,
                email: otherParticipant.email,
                avatar: otherParticipant.ProfileImageURL
              },
              propertyId: conv.propertyId,
              propertyTitle: conv.propertyTitle,
              propertyImage: conv.propertyImage,
              lastActivity: conv.lastActivity,
              isActive: conv.isActive,
              unreadCount: 0, // This would be calculated separately
              createdAt: conv.createdAt
            };
          });

          socket.emit('conversations:list', {
            success: true,
            conversations: formattedConversations,
            pagination: {
              limit: parseInt(limit),
              offset: parseInt(offset),
              total: totalConversations,
              hasMore: offset + conversations.length < totalConversations
            }
          });

          logger.info(`📋 Sent ${formattedConversations.length} conversations to user ${socket.userId}`);

        } catch (dbError) {
          logger.error(`Database error in handleListConversations: ${dbError.message}`);
          throw new Error('Failed to fetch conversations from database');
        }

      } catch (error) {
        logger.error(`List conversations error: ${error}`);
        socket.emit('error', { event: 'conversations:list', message: error.message });
      }
    }
  };
};

module.exports = { createHandlers };