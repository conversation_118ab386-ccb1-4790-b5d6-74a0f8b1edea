const FavoriteController = require('../controllers/favorites.controller');
const { authHook } = require('../../../hooks/auth.hook');

async function favoritesRoutes(fastify, options) {
  const favoriteController = new FavoriteController();

  // Add listing to favorites
  fastify.post('/listings/:id/favorite', {
    preHandlers: [authHook],
    schema: {
      tags: ['favorites'],
      summary: 'Add listing to favorites',
      description: 'Add a specific listing to user\'s favorites',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        201: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: { type: 'object' }
          }
        }
      }
    },
    handler: favoriteController.addToFavorites.bind(favoriteController)
  });

  // Remove listing from favorites
  fastify.delete('/listings/:id/favorite', {
    preHandlers: [authHook],
    schema: {
      tags: ['favorites'],
      summary: 'Remove listing from favorites',
      description: 'Remove a specific listing from user\'s favorites',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    },
    handler: favoriteController.removeFromFavorites.bind(favoriteController)
  });


  // Check if listing is favorited
  fastify.get('/listings/:id/favorite-status', {
    preHandlers: [authHook],
    schema: {
      tags: ['favorites'],
      summary: 'Check favorite status',
      description: 'Check if a specific listing is favorited by the authenticated user',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                isFavorited: { type: 'boolean' },
                favoriteId: { type: 'string', nullable: true }
              }
            }
          }
        }
      }
    },
    handler: favoriteController.checkFavoriteStatus.bind(favoriteController)
  });

  // Get user favorites
  fastify.get('/favorites', {
    preHandlers: [authHook],
    schema: {
      tags: ['favorites'],
      summary: 'Get user favorites',
      description: 'Debug endpoint to test favorites functionality',
      security: [{ bearerAuth: [] }]
    },
    handler: favoriteController.getFavorites.bind(favoriteController)
  });
}

module.exports = favoritesRoutes;
