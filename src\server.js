const path = require('path');
require('dotenv').config();

const { createApp } = require('./app');
const { logger } = require('./utils/logger');
const { getDatabase, initializeDatabase } = require('./config/database.config');
const { initializeCache } = require('./config/cache.config');
const { initializeQueues } = require('./config/queue.config');
const { loadDependencies } = require('./di');
const { startGrpcServer } = require('./edge/grpc/server');
// const { startMqttClient } = require('./edge/mqtt/client');
const { setupPrometheus } = require('./monitoring/prometheus');

const startServer = async () => {
  try {
    logger.info('🚀 Starting Enterprise Node.js Application...');

    // Initialize infrastructure
    logger.info('📦 Initializing infrastructure...');
    await initializeDatabase();
    await initializeCache();
    await initializeQueues();

    // Load dependencies
    logger.info('🔧 Loading dependencies...');
    const container = await loadDependencies(getDatabase('write'), getDatabase());

    // Setup monitoring
    logger.info('📊 Setting up monitoring...');
    setupPrometheus();

    // Create Fastify app
    logger.info('🌐 Creating Fastify application...');
    const app = await createApp(container);

    // Start edge handlers
    logger.info('🔌 Starting edge handlers...');
    await startGrpcServer(container, app.jwt);
    // await startMqttClient(container);

    // Start HTTP server
    const port = process.env.PORT || 7000;
    await app.listen({ port, host: '0.0.0.0' });

    logger.info(`✅ Server running on port ${port}`);
    logger.info(`📚 API Documentation: http://localhost:${port}/documentation`);
    logger.info(`📊 Metrics: http://localhost:${port}/metrics`);

    // Graceful shutdown
    const gracefulShutdown = async (signal) => {
      logger.info(`${signal} received, shutting down gracefully...`);
      await app.close();
      process.exit(0);
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    logger.error('Failed to start server:', error);
    console.error('Failed to start server', error)
    process.exit(1);
  }
};

// Handle unhandled rejections
process.on('unhandledRejection', (err) => {
  logger.error(`Unhandled rejection: ${err}`);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error(`Uncaught exception:, ${err}`);
  process.exit(1);
});

// Start the server
startServer();