const { DataTypes } = require('sequelize');

const defineListingViewModel = (sequelize) => {
  const ListingView = sequelize.define('ListingView', {
    ViewID: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    ListingID: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'listings',
        key: 'ListingID'
      }
    },
    UserID: {
      type: DataTypes.UUID,
      allowNull: true, // Allow null for anonymous users
      references: {
        model: 'users',
        key: 'id'
      }
    },
    IPAddress: {
      type: DataTypes.STRING(45), // Support both IPv4 and IPv6
      allowNull: true
    },
    UserAgent: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    SessionID: {
      type: DataTypes.STRING(255),
      allowNull: true // For tracking anonymous sessions
    },
    ViewedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      allowNull: false
    },
    // Additional metadata
    Referrer: {
      type: DataTypes.STRING(500),
      allowNull: true
    },
    DeviceType: {
      type: DataTypes.ENUM('desktop', 'mobile', 'tablet', 'unknown'),
      defaultValue: 'unknown'
    }
  }, {
    tableName: 'listing_views',
    timestamps: false,
    indexes: [
      // Composite index for unique user views
      {
        fields: ['ListingID', 'UserID'],
        name: 'idx_listing_user_view'
      },
      // Index for anonymous user tracking
      {
        fields: ['ListingID', 'IPAddress', 'UserAgent'],
        name: 'idx_listing_anonymous_view'
      },
      // Index for session-based tracking
      {
        fields: ['ListingID', 'SessionID'],
        name: 'idx_listing_session_view'
      },
      // Time-based index for analytics
      {
        fields: ['ViewedAt'],
        name: 'idx_viewed_at'
      },
      // Listing-specific views
      {
        fields: ['ListingID', 'ViewedAt'],
        name: 'idx_listing_time'
      }
    ]
  });

  ListingView.associate = (models) => {
    // Association with Listing
    ListingView.belongsTo(models.Listing, {
      foreignKey: 'ListingID',
      as: 'Listing'
    });

    // Association with User (nullable)
    ListingView.belongsTo(models.User, {
      foreignKey: 'UserID',
      as: 'User'
    });
  };

  return ListingView;
};

module.exports = defineListingViewModel;
