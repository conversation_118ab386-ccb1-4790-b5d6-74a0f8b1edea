const { DataTypes } = require('sequelize');

const defineCategoryModel = (sequelize) => {
  const Category = sequelize.define('Category', {
    CategoryID: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    Name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true
    },
    Description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    IconURL: {
      type: DataTypes.STRING,
      allowNull: true
    },
    IsActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    CreatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    UpdatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'categories',
    timestamps: false,
    hooks: {
      beforeUpdate: (category) => {
        category.UpdatedAt = new Date();
      }
    }
  });

  Category.associate = (models) => {
    Category.hasMany(models.Subcategory, {
      foreignKey: 'CategoryID',
      as: 'Subcategories',
      onDelete: 'CASCADE'
    });

    Category.hasMany(models.Listing, {
      foreignKey: 'CategoryID',
      as: 'Listings'
    });
  };

  return Category;
};

module.exports = defineCategoryModel;
