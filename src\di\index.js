const { createContainer } = require('./container');
const { loadAllDependencies, loadDependenciesWithConfig, getLoaderStats } = require('./loader');
const { logger } = require('../utils/logger');
const { masterDb } = require('../config/database.config');

/**
 * Main dependency loading function
 * Creates container and loads all dependencies
 */
const loadDependencies = async (masterDb, replicaDb, baseDir = __dirname) => {
  try {
    // Create container
    const container = createContainer();

    // Load all dependencies
    await loadAllDependencies(container, masterDb, replicaDb, baseDir);

    // Log final stats
    const stats = getLoaderStats(container, baseDir);
    logger.info('📊 Loading complete:', {
      totalDependencies: stats.totalDependencies,
      singletonInstances: stats.singletonInstances,
      availableDependencies: stats.dependencies
    });

    return container;

  } catch (error) {
    logger.error('💥 Critical error during dependency loading:', error);
    throw error;
  }
};

/**
 * Load dependencies with custom configuration
 */
const loadDependenciesWithOptions = async (options = {}) => {
  const { baseDir = __dirname } = options;

  try {
    const container = createContainer();

    // Load dependencies with custom config
    await loadDependenciesWithConfig(container, options);

    // Log final stats
    const stats = getLoaderStats(container, baseDir);
    logger.info('📊 Custom loading complete:', {
      totalDependencies: stats.totalDependencies,
      singletonInstances: stats.singletonInstances,
      availableDependencies: stats.dependencies
    });

    return container;

  } catch (error) {
    logger.error('💥 Failed to load dependencies with custom config:', error);
    throw error;
  }
};

/**
 * Health check for container
 */
const healthCheck = (container) => {
  const stats = container.getStats();
  const requiredDependencies = ['models', 'logger'];

  const missing = requiredDependencies.filter(dep => !container.has(dep));

  if (missing.length > 0) {
    logger.warn(`⚠️ Missing critical dependencies: ${missing.join(', ')}`);
    return {
      healthy: false,
      missing,
      stats
    };
  }

  logger.info('✅ Container health check passed');
  return {
    healthy: true,
    missing: [],
    stats
  };
};

module.exports = {
  loadDependencies,
  loadDependenciesWithOptions,
  healthCheck
};