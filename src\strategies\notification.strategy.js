const { logger } = require('../utils/logger');
const path = require('path');
const fs = require('fs').promises;


// Email templates
const emailTemplates = {
  'welcome': {
    subject: 'Welcome to {{appName}}!',
    template: `
      <h1>Welcome {{name}}!</h1>
      <p>Thank you for joining {{appName}}. We're excited to have you on board.</p>
      <p>Get started by exploring our features and setting up your profile.</p>
    `
  },
  'email-verification': {
    subject: 'Verify Your Email Address',
    template: `
      <h1>Hi {{name}},</h1>
      <p>Please verify your email address by clicking the link below:</p>
      <a href="{{verificationLink}}" style="padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;">Verify Email</a>
      <p>This link will expire in 24 hours.</p>
    `
  },
  'password-reset': {
    subject: 'Password Reset Request',
    template: `
      <h1>Hi {{name}},</h1>
      <p>We received a request to reset your password. Click the link below to reset it:</p>
      <a href="{{resetLink}}" style="padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;">Reset Password</a>
      <p>If you didn't request this, please ignore this email.</p>
      <p>This link will expire in 1 hour.</p>
    `
  },
  'order-confirmation': {
    subject: 'Order Confirmation - #{{orderNumber}}',
    template: `
      <h1>Thank you for your order, {{user.firstName}}!</h1>
      <p>Your order #{{order.orderNumber}} has been confirmed.</p>
      <h2>Order Details:</h2>
      <ul>
        {{#each order.items}}
        <li>{{this.name}} x {{this.quantity}} - {{#order.price}}</li>
        {{/each}}
      </ul>
      <p><strong>Total: {{#order.total}}</strong></p>
      <p>We'll send you another email when your order ships.</p>
    `
  },
  'order-shipped': {
    subject: 'Your Order Has Been Shipped! - #{{orderNumber}}',
    template: `
      <h1>Great news, {{user.firstName}}!</h1>
      <p>Your order #{{order.orderNumber}} has been shipped.</p>
      <p><strong>Tracking Number:</strong> {{trackingNumber}}</p>
      <p><strong>Carrier:</strong> {{carrier}}</p>
      <a href="https://track.example.com/{{trackingNumber}}" style="padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;">Track Package</a>
    `
  }
};

// Notification channels
const notificationChannels = {
  email: {
    async send(data) {
      const { to, subject, template, data: templateData } = data;
      
      logger.info(`Sending email to ${to}: ${subject}`);
      
      // In production, integrate with email service (SendGrid, AWS SES, etc.)
      // For now, simulate email sending
      
      let emailTemplate = emailTemplates[template];
      if (!emailTemplate) {
        throw new Error(`Email template '${template}' not found`);
      }

      // Simple template rendering (in production, use a proper template engine)
      let html = emailTemplate.template;
      let emailSubject = emailTemplate.subject;
      
      // Replace placeholders
      Object.entries(templateData).forEach(([key, value]) => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        html = html.replace(regex, value);
        emailSubject = emailSubject.replace(regex, value);
      });

      return {
        success: true,
        messageId: `email_${Date.now()}`,
        to,
        subject: emailSubject
      };
    }
  },

  sms: {
    async send(data) {
      const { to, message } = data;
      
      logger.info(`Sending SMS to ${to}`);
      
      // In production, integrate with SMS service (Twilio, AWS SNS, etc.)
      
      if (!to || !message) {
        throw new Error('Phone number and message are required for SMS');
      }

      return {
        success: true,
        messageId: `sms_${Date.now()}`,
        to,
        characterCount: message.length
      };
    }
  },

  push: {
    async send(data) {
      const { id, title, body, data: pushData } = data;
      
      logger.info(`Sending push notification to user ${id}: ${title}`);
      
      // In production, integrate with push notification service (FCM, APNS, etc.)
      
      return {
        success: true,
        notificationId: `push_${Date.now()}`,
        id,
        title
      };
    }
  },

  webhook: {
    async send(data) {
      const { url, payload, headers = {} } = data;
      
      logger.info(`Sending webhook to ${url}`);
      
      // In production, make actual HTTP request
      const axios = require('axios');
      
      try {
        const response = await axios.post(url, payload, { headers });
        
        return {
          success: true,
          statusCode: response.status,
          webhookId: `webhook_${Date.now()}`
        };
      } catch (error) {
        throw new Error(`Webhook failed: ${error.message}`);
      }
    }
  }
};

// Notification types mapping
const notificationTypes = {
  'welcome': {
    channels: ['email'],
    template: 'welcome',
    handler: async (data) => ({
      to: data.user.email,
      subject: 'Welcome!',
      template: 'welcome',
      data: {
        name: data.user.firstName,
        appName: 'Our App'
      }
    })
  },

  'order-confirmation': {
    channels: ['email', 'sms'],
    template: 'order-confirmation',
    handler: async (data) => ({
      email: {
        to: data.user.email,
        template: 'order-confirmation',
        data: {
          user: data.user,
          order: data.order
        }
      },
      sms: {
        to: data.user.phone,
        message: `Order #${data.order.orderNumber} confirmed! Total: $${data.order.total}`
      }
    })
  },

  'order-shipped': {
    channels: ['email', 'push'],
    template: 'order-shipped',
    handler: async (data) => ({
      email: {
        to: data.user.email,
        template: 'order-shipped',
        data: {
          user: data.user,
          order: data.order,
          trackingNumber: data.trackingNumber,
          carrier: data.carrier
        }
      },
      push: {
        id: data.user.id,
        title: 'Order Shipped!',
        body: `Your order #${data.order.orderNumber} is on its way!`,
        data: {
          orderId: data.order.id,
          trackingNumber: data.trackingNumber
        }
      }
    })
  }
}; // Added semicolon here

// Strategy implementation
const createNotificationStrategy = () => {
  return {
    async execute(type, data) {
      try {
        // Handle direct channel execution
        if (notificationChannels[type]) {
          return await notificationChannels[type].send(data);
        }

        // Handle notification type execution
        const notificationType = notificationTypes[type];
        if (!notificationType) {
          throw new Error(`Unknown notification type: ${type}`);
        }

        const results = {};
        const notificationData = await notificationType.handler(data);

        // Send through each configured channel
        for (const channel of notificationType.channels) {
          try {
            const channelData = notificationData[channel] || notificationData;
            
            if (channelData && notificationChannels[channel]) {
              results[channel] = await notificationChannels[channel].send(channelData);
            }
          } catch (error) {
            logger.error(`Failed to send ${type} notification via ${channel}:`, error);
            results[channel] = {
              success: false,
              error: error.message
            };
          }
        }

        return {
          success: true,
          type,
          results
        };

      } catch (error) {
        logger.error(`Notification strategy execution failed:`, error);
        throw error;
      }
    },

    // Add new notification channel
    addChannel(name, handler) {
      notificationChannels[name] = handler;
    },

    // Add new notification type
    addType(name, config) {
      notificationTypes[name] = config;
    },

    // Get available channels
    getChannels() {
      return Object.keys(notificationChannels);
    },

    // Get available types
    getTypes() {
      return Object.keys(notificationTypes);
    },

    // Add email template
    addEmailTemplate(name, template) {
      emailTemplates[name] = template;
    }
  };
};

module.exports = { createNotificationStrategy };