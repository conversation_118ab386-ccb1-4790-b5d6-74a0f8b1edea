const { getModels } = require('../models');

class CategoryRepository {
  constructor() {
    this.models = null;
  }

  getModels(operation = 'read') {
    return getModels(operation);
  }

  async create(categoryData) {
    const models = this.getModels('write');
    return await models.Category.create(categoryData);
  }

  async findAll(includeSubcategories = true) {
    const models = this.getModels('read');
    const options = {
      where: { IsActive: true },
      order: [['Name', 'ASC']]
    };

    if (includeSubcategories) {
      options.include = [
        {
          model: models.Subcategory,
          as: 'Subcategories',
          where: { IsActive: true },
          required: false,
          attributes: ['SubcategoryID', 'Name', 'Description', 'IconURL']
        }
      ];
    }

    return await models.Category.findAll(options);
  }

  async findById(id, includeSubcategories = true) {
    const models = this.getModels('read');
    const options = {
      where: { CategoryID: id, IsActive: true }
    };

    if (includeSubcategories) {
      options.include = [
        {
          model: models.Subcategory,
          as: 'Subcategories',
          where: { IsActive: true },
          required: false,
          attributes: ['SubcategoryID', 'Name', 'Description', 'IconURL']
        }
      ];
    }

    return await models.Category.findOne(options);
  }

  async update(id, updateData) {
    const models = this.getModels('write');
    const [updatedRowsCount] = await models.Category.update(updateData, {
      where: { CategoryID: id }
    });
    
    if (updatedRowsCount === 0) {
      return null;
    }
    
    return await this.findById(id);
  }

  async delete(id) {
    const models = this.getModels('write');
    return await models.Category.update(
      { IsActive: false },
      { where: { CategoryID: id } }
    );
  }

  async createSubcategory(subcategoryData) {
    const models = this.getModels('write');
    return await models.Subcategory.create(subcategoryData);
  }

  async findSubcategoriesByCategory(categoryId) {
    const models = this.getModels('read');
    return await models.Subcategory.findAll({
      where: { 
        CategoryID: categoryId,
        IsActive: true 
      },
      order: [['Name', 'ASC']]
    });
  }

  async findSubcategoryById(id) {
    const models = this.getModels('read');
    return await models.Subcategory.findOne({
      where: { 
        SubcategoryID: id,
        IsActive: true 
      },
      include: [
        {
          model: models.Category,
          as: 'Category',
          attributes: ['CategoryID', 'Name', 'IconURL']
        }
      ]
    });
  }

  async updateSubcategory(id, updateData) {
    const models = this.getModels('write');
    const [updatedRowsCount] = await models.Subcategory.update(updateData, {
      where: { SubcategoryID: id }
    });
    
    if (updatedRowsCount === 0) {
      return null;
    }
    
    return await this.findSubcategoryById(id);
  }

  async deleteSubcategory(id) {
    const models = this.getModels('write');
    return await models.Subcategory.update(
      { IsActive: false },
      { where: { SubcategoryID: id } }
    );
  }
}

module.exports = CategoryRepository;
