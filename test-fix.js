// Test script to verify the repository fix
const { createListingRepository } = require('./src/repositories/listing.repository');
const { createListingViewService } = require('./src/services/listingView.service');
const { createListingViewRepository } = require('./src/repositories/listingView.repository');

// Mock models for testing
const mockModels = {
  Listing: {
    findOne: () => Promise.resolve({ ListingID: 'test-id', Title: 'Test Listing' }),
    create: () => Promise.resolve({ ListingID: 'test-id' }),
    findAndCountAll: () => Promise.resolve({ count: 0, rows: [] }),
    update: () => Promise.resolve([1]),
    destroy: () => Promise.resolve(1),
    increment: () => Promise.resolve([1])
  },
  ListingView: {
    findOne: () => Promise.resolve(null),
    create: () => Promise.resolve({ ViewID: 'test-view-id' }),
    count: () => Promise.resolve(0),
    findAll: () => Promise.resolve([]),
    destroy: () => Promise.resolve(0)
  },
  Category: {},
  Subcategory: {}
};

console.log('Testing repository creation...');

try {
  // Test listing repository creation
  const listingRepository = createListingRepository({ models: mockModels });
  console.log('✅ Listing repository created successfully');
  console.log('Available methods:', Object.keys(listingRepository));

  // Test listing view repository creation
  const listingViewRepository = createListingViewRepository({ models: mockModels });
  console.log('✅ Listing view repository created successfully');

  // Test service creation with both repositories
  const listingViewService = createListingViewService({ 
    listingViewRepository, 
    listingRepository 
  });
  console.log('✅ Listing view service created successfully');
  console.log('Available service methods:', Object.keys(listingViewService));

  console.log('\n🎉 All tests passed! The fix is working correctly.');
  
} catch (error) {
  console.error('❌ Test failed:', error.message);
  console.error(error.stack);
}
