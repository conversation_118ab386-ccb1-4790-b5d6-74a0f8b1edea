const Joi = require('joi');

const chatJoin = Joi.object({
    conversationId: Joi.string().required().messages({
        'string.empty': 'Conversation ID cannot be empty',
        'any.required': 'Conversation ID is required'
    })
});

const chatLeave = Joi.object({
    conversationId: Joi.string().required().messages({
        'string.empty': 'Conversation ID cannot be empty',
        'any.required': 'Conversation ID is required'
    })
});

const messageSend = Joi.object({
    conversationId: Joi.string().required().messages({
        'string.empty': 'Conversation ID cannot be empty',
        'any.required': 'Conversation ID is required'
    }),
    content: Joi.string().required().min(1).max(4000).messages({
        'string.empty': 'Message content cannot be empty',
        'any.required': 'Message content is required',
        'string.min': 'Message cannot be empty',
        'string.max': 'Message is too long (max 4000 characters)'
    }),
    recipientId: Joi.string().uuid().required().messages({
        'string.empty': 'Recipient ID cannot be empty',
        'any.required': 'Recipient ID is required',
        'string.guid': 'Invalid recipient ID format'
    }),
    type: Joi.string().valid('text', 'image', 'file').default('text').messages({
        'any.only': 'Message type must be text, image, or file'
    }),
    metadata: Joi.object().optional()
});

const messageMarkRead = Joi.object({
    messageId: Joi.string().required().messages({
        'string.empty': 'Message ID cannot be empty',
        'any.required': 'Message ID is required'
    }),
    conversationId: Joi.string().optional()
});

const conversationGet = Joi.object({
    conversationId: Joi.string().required().messages({
        'string.empty': 'Conversation ID cannot be empty',
        'any.required': 'Conversation ID is required'
    }),
    limit: Joi.number().integer().min(1).max(100).default(50).optional(),
    offset: Joi.number().integer().min(0).default(0).optional()
});

const conversationsList = Joi.object({
    limit: Joi.number().integer().min(1).max(50).default(20).optional(),
    offset: Joi.number().integer().min(0).default(0).optional()
});

// Typing events (no validation needed as they're simple)
const typingStart = Joi.object({
    conversationId: Joi.string().required().messages({
        'string.empty': 'Conversation ID cannot be empty',
        'any.required': 'Conversation ID is required'
    })
});

const typingStop = Joi.object({
    conversationId: Joi.string().required().messages({
        'string.empty': 'Conversation ID cannot be empty',
        'any.required': 'Conversation ID is required'
    })
});

module.exports = {
    chatJoin,
    chatLeave,
    messageSend,
    messageMarkRead,
    conversationGet,
    conversationsList,
    typingStart,
    typingStop
};
