const { logger } = require('../../../utils/logger');

const chatController = {
    async getUserConversations(request, reply) {
        try {
            const { page = 1, limit = 20 } = request.query;
            const userId = request.user.id; // From auth middleware

            const chatService = request.server.container.resolve('chatService');
            const result = await chatService.getUserConversationsWithPagination(userId, {
                limit: parseInt(limit),
                offset: (parseInt(page) - 1) * parseInt(limit)
            });

            reply.send({
                success: true,
                conversations: result.conversations,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total: result.total,
                    hasMore: result.hasMore
                }
            });

        } catch (error) {
            logger.error('Get user conversations failed:', {
                error: error.message,
                userId: request.user?.id
            });
            throw error;
        }
    },

    async getConversation(request, reply) {
        try {
            const { conversationId } = request.params;
            const { page = 1, limit = 50 } = request.query;
            const userId = request.user.id;

            const chatService = request.server.container.resolve('chatService');
            const result = await chatService.getConversationWithMessages(conversationId, {
                userId,
                limit: parseInt(limit),
                offset: (parseInt(page) - 1) * parseInt(limit)
            });

            reply.send({
                success: true,
                conversation: result.conversation,
                messages: result.messages,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total: result.total,
                    hasMore: result.hasMore
                }
            });

        } catch (error) {
            logger.error('Get conversation failed:', {
                error: error.message,
                userId: request.user?.id,
                conversationId: request.params.conversationId
            });
            throw error;
        }
    },

    async createConversation(request, reply) {
        try {
            const { participantId, propertyId } = request.body;
            const userId = request.user.id;

            if (!participantId) {
                return reply.code(400).send({
                    success: false,
                    message: 'participantId is required'
                });
            }

            const chatService = request.server.container.resolve('chatService');
            const result = await chatService.createConversation(userId, participantId, propertyId);

            reply.code(201).send({
                success: true,
                conversation: result.conversation
            });

        } catch (error) {
            logger.error('Create conversation failed:', {
                error: error.message,
                userId: request.user?.id,
                body: request.body
            });
            throw error;
        }
    },

    async sendMessage(request, reply) {
        try {
            const { conversationId } = request.params;
            const { content, recipientId, type = 'text', metadata } = request.body;
            const userId = request.user.id;

            if (!content || !recipientId) {
                return reply.code(400).send({
                    success: false,
                    message: 'content and recipientId are required'
                });
            }

            const chatService = request.server.container.resolve('chatService');
            const messageData = {
                conversationId,
                senderId: userId,
                recipientId,
                content,
                type,
                metadata
            };

            const result = await chatService.createMessage(messageData);

            // Emit socket event if available
            const io = request.server.io;
            if (io && io.of('/chat')) {
                const messageForBroadcast = {
                    id: result.id,
                    conversationId: result.conversationId,
                    senderId: result.senderId,
                    recipientId: result.recipientId,
                    content: result.content,
                    type: result.type,
                    isRead: result.isRead,
                    createdAt: result.createdAt,
                    metadata: result.metadata,
                    sender: result.sender
                };

                // Broadcast to conversation room
                io.of('/chat').to(conversationId).emit('message:receive', messageForBroadcast);

                // Send to recipient's personal room if not in conversation room
                io.of('/chat').to(`user:${recipientId}`).emit('message:notification', {
                    conversationId,
                    message: messageForBroadcast,
                    sender: result.sender
                });
            }

            reply.code(201).send({
                success: true,
                message: result
            });

        } catch (error) {
            logger.error('Send message failed:', {
                error: error.message,
                userId: request.user?.id,
                conversationId: request.params.conversationId,
                body: request.body
            });
            throw error;
        }
    },

    async markMessageAsRead(request, reply) {
        try {
            const { messageId } = request.params;
            const userId = request.user.id;

            const chatService = request.server.container.resolve('chatService');
            const result = await chatService.markMessageAsRead(messageId, userId);

            if (!result.success) {
                return reply.code(400).send({
                    success: false,
                    message: result.message || 'Failed to mark message as read'
                });
            }

            // Emit socket event if available
            const io = request.server.io;
            if (io && io.of('/chat') && result.conversationId) {
                io.of('/chat').to(result.conversationId).emit('message:read', {
                    messageId,
                    conversationId: result.conversationId,
                    readBy: userId,
                    readAt: new Date()
                });
            }

            reply.send({
                success: true,
                message: 'Message marked as read'
            });

        } catch (error) {
            logger.error('Mark message as read failed:', {
                error: error.message,
                userId: request.user?.id,
                messageId: request.params.messageId
            });
            throw error;
        }
    },

    async getChatStats(request, reply) {
        try {
            const userId = request.user.id;

            const chatService = request.server.container.resolve('chatService');
            const result = await chatService.getConversationStats(userId);

            reply.send({
                success: true,
                stats: result.stats
            });

        } catch (error) {
            logger.error('Get chat stats failed:', {
                error: error.message,
                userId: request.user?.id
            });
            throw error;
        }
    }
};

module.exports = chatController;
