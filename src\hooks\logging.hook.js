const { logger, logRequest } = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

const loggingHook = async (request, reply) => {
  // Generate request ID if not present
  if (!request.id) {
    request.id = request.headers['x-request-id'] || uuidv4();
  }

  // Set request ID in response headers
  reply.header('x-request-id', request.id);

  // Log request start
  logger.info({
    type: 'request_start',
    requestId: request.id,
    method: request.method,
    url: request.url,
    userAgent: request.headers['user-agent'],
    ip: request.ip
  });
};

// Request context hook - adds contextual information
const contextHook = async (request, reply) => {
  request.context = {
    startTime: Date.now(),
    requestId: request.id,
    userAgent: request.headers['user-agent'],
    ip: request.ip,
    correlationId: request.headers['x-correlation-id'] || uuidv4()
  };

  // Set correlation ID in response
  reply.header('x-correlation-id', request.context.correlationId);
};

// Performance monitoring hook
const performanceHook = async (request, reply) => {
  const segments = {
    auth: 0,
    validation: 0,
    business: 0,
    database: 0,
    total: 0
  };

  request.performance = {
    start: (segment) => {
      segments[segment] = Date.now();
    },
    end: (segment) => {
      if (segments[segment]) {
        const duration = Date.now() - segments[segment];
        logger.debug({
          type: 'performance_segment',
          requestId: request.id,
          segment,
          duration
        });
      }
    },
    getMetrics: () => segments
  };
};

// Combined onSend hook for logging and performance
const onSendHook = async (request, reply, payload) => {
  // Guard clause to handle cases where context wasn't initialized
  if (!request.context || !request.context.startTime) {
    logger.warn({
      type: 'missing_context',
      requestId: request.id,
      method: request.method,
      url: request.url,
      statusCode: reply.statusCode
    }, 'Request context not initialized, skipping performance logging');
    return payload;
  }

  const duration = Date.now() - request.context.startTime;

  // Logging hook onSend logic
  logRequest(request, reply, duration);
  reply.header('x-response-time', `${duration}ms`);

  if (duration > 1000) {
    logger.warn({
      type: 'slow_request',
      requestId: request.id,
      method: request.method,
      url: request.url,
      duration,
      statusCode: reply.statusCode
    }, `Slow request detected: ${duration}ms`);
  }

  // Performance hook onSend logic
  if (request.performance && request.context && request.context.startTime) {
    const segments = request.performance.getMetrics();
    segments.total = Date.now() - request.context.startTime;
    
    if (segments.total > 100) {
      logger.info({
        type: 'request_performance',
        requestId: request.id,
        metrics: segments
      });
    }
  }

  return payload;
};

module.exports = {
  loggingHook,
  contextHook,
  performanceHook,
  onSendHook
};