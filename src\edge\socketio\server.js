const { logger } = require('../../utils/logger');
const { authMiddleware, defineSocketPerformanceMiddleware } = require('./middleware');
const { createHandlers } = require('./handlers');


const setupSocketServer = (io, container) => {
  // Apply authentication middleware
  io.use(authMiddleware(io, container)); // pass io here to deal with
  io.use(defineSocketPerformanceMiddleware(container)); // pass io here to deal with

  // Create namespace for different features
  const chatNamespace = io.of('/chat');
  const notificationNamespace = io.of('/notifications');
  const trackingNamespace = io.of('/tracking');

  const activeUsers = new Map(); // Track online users

  // Get handlers
  const handlers = createHandlers(io, container);


  // Main namespace
  io.on('connection', (socket) => {
    logger.info(`Client connected: ${socket.id}`, {
      userId: socket.userId,
      userRole: socket.userRole
    });

    // Join user room
    if (socket.userId) {
      socket.join(`user:${socket.userId}`);
    }


    // Register event handlers
    // socket.on('subscribe', (data) => handlers.handleSubscribe(socket, data));
    // socket.on('unsubscribe', (data) => handlers.handleUnsubscribe(socket, data));
    // socket.on('register', (data) => handlers.handleCreateUser(socket, data));
    // socket.on('login', (data) => handlers.handleUserLogin(socket, data));

    // // User events
    // socket.on('user:status', (data) => handlers.handleUserStatus(socket, data));
    // socket.on('user:get', (data) => handlers.handleGetUser(socket, data));
    // socket.on('user:update', (data) => handlers.handleUpdateUser(socket, data));
    // socket.on('user:delete', (data) => handlers.handleDeleteUser(socket, data));


    // Handle disconnection
    socket.on('disconnect', (reason) => {
      logger.info(`Client disconnected: ${socket.id}`, { reason });

      // Clean up active users
      if (socket.userId) {
        activeUsers.delete(socket.userId);
      }

      handlers.handleDisconnect(socket);
    });

    // Error handling
    socket.on('error', (error) => {
      logger.error(`Socket error for ${socket.id}:`, error);
    });
  });


  // Chat namespace - Using handlers.js
  chatNamespace.use(authMiddleware(io, container));
  chatNamespace.on('connection', (socket) => {
    logger.info(`Chat client connected: ${socket.id}`);

    // Add user to active users
    if (socket.userId) {
      activeUsers.set(socket.userId, {
        socketId: socket.id,
        email: socket.userEmail,
        connectedAt: new Date(),
        namespace: 'chat'
      });

      // Join user room for direct messaging
      socket.join(`user:${socket.userId}`);

      // Broadcast user online status
      chatNamespace.emit('user:online', {
        userId: socket.userId,
        email: socket.userEmail
      });
    }

    // Chat event handlers
    socket.on('chat:join', (data) => handlers.handleChatJoin(socket, data));
    socket.on('chat:leave', (data) => handlers.handleChatLeave(socket, data));
    socket.on('message:send', (data) => handlers.handleMessageSend(socket, data));
    socket.on('typing:start', (data) => handlers.handleTypingStart(socket, data));
    socket.on('typing:stop', (data) => handlers.handleTypingStop(socket, data));
    socket.on('message:mark-read', (data) => handlers.handleMessageMarkRead(socket, data));

    // Additional chat events
    socket.on('conversation:get', (data) => handlers.handleGetConversation(socket, data));
    socket.on('conversations:list', (data) => handlers.handleListConversations(socket, data));

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      logger.info(`Chat client disconnected: ${socket.id}`, { reason });

      if (socket.userId) {
        activeUsers.delete(socket.userId);

        // Broadcast user offline status
        chatNamespace.emit('user:offline', {
          userId: socket.userId,
          lastSeen: new Date()
        });
      }
    });
  });

  // Notification namespace
  notificationNamespace.use(authMiddleware(io, container));
  notificationNamespace.on('connection', (socket) => {
    logger.info(`Notification client connected: ${socket.id}`);

    socket.on('notification:mark-read', async (data) => {
      try {
        await handlers.handleNotificationRead(socket, data);
        socket.emit('notification:marked-read', { notificationId: data.notificationId });
      } catch (error) {
        socket.emit('error', { message: error.message });
      }
    });

    socket.on('notification:mark-all-read', async () => {
      try {
        await handlers.handleMarkAllNotificationsRead(socket);
        socket.emit('notification:all-marked-read');
      } catch (error) {
        socket.emit('error', { message: error.message });
      }
    });
  });

  // Tracking namespace (public)
  trackingNamespace.on('connection', (socket) => {
    logger.info(`Tracking client connected: ${socket.id}`);

    socket.on('track:order', async (data) => {
      try {
        const trackingInfo = await handlers.handlePublicOrderTracking(socket, data);
        socket.emit('tracking:update', trackingInfo);

        // Join tracking room
        socket.join(`tracking:${data.trackingNumber}`);
      } catch (error) {
        socket.emit('error', { message: 'Invalid tracking number' });
      }
    });
  });

  // Subscribe to Redis pub/sub for cross-instance communication
  setupRedisPubSub(io, container);

  return io;
};

const setupRedisPubSub = (io, container) => {
  const { getRedis } = require('../../config/cache.config');
  const subscriber = getRedis().duplicate();

  subscriber.subscribe('socket:broadcast');

  subscriber.on('message', (channel, message) => {
    try {
      const data = JSON.parse(message);

      switch (data.type) {
        case 'order:status-changed':
          io.to(`user:${data.userId}`).emit('order:status-changed', data.payload);
          io.of('/tracking').to(`tracking:${data.trackingNumber}`).emit('tracking:update', data.payload);
          break;

        case 'notification:new':
          io.of('/notifications').to(`user:${data.userId}`).emit('notification:new', data.payload);
          break;

        case 'user:updated':
          io.to(`user:${data.userId}`).emit('user:updated', data.payload);
          break;

        // Chat-related Redis events for multi-server support
        case 'chat:message-received':
          io.of('/chat').to(data.conversationId).emit('message:receive', data.payload);
          break;

        case 'chat:message-read':
          io.of('/chat').to(data.conversationId).emit('message:read', data.payload);
          break;

        case 'chat:user-typing':
          io.of('/chat').to(data.conversationId).emit('typing:started', data.payload);
          break;

        case 'chat:user-stopped-typing':
          io.of('/chat').to(data.conversationId).emit('typing:stopped', data.payload);
          break;

        default:
          logger.warn(`Unknown broadcast type: ${data.type}`);
      }
    } catch (error) {
      logger.error('Redis pub/sub error:', error);
    }
  });
};

module.exports = setupSocketServer;