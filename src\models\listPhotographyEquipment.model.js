const { DataTypes } = require('sequelize');

const definePhotographyEquipmentModel = (sequelize) => {
  const PhotographyEquipment = sequelize.define('PhotographyEquipment', {
    EquipmentID: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    ListingID: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'listings',
        key: 'ListingID'
      }
    },
    EquipmentType: { type: DataTypes.STRING, allowNull: false },
    BrandModel: { type: DataTypes.STRING, allowNull: false },
    // Condition: { type: DataTypes.ENUM('new', 'likeNew', 'used'), allowNull: false },
    UsageInstructionsAttached: { type: DataTypes.BOOLEAN, allowNull: false, defaultValue: false },
    AccessoriesIncluded: { type: DataTypes.STRING, allowNull: true },
    //PickupLocation: { type: DataTypes.STRING, allowNull: true }
  }, {
    tableName: 'photography_equipment',
    timestamps: false
  });

  PhotographyEquipment.associate = (models) => {
    PhotographyEquipment.belongsTo(models.Listing, {
      foreignKey: 'ListingID',
      as: 'Listing'
    });
  };

  return PhotographyEquipment;
};

module.exports = definePhotographyEquipmentModel;
