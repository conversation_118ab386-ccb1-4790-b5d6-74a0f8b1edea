const { Sequelize } = require('sequelize');
const { logger } = require('../utils/logger');

let masterDb = null;
let replicaDb = null;

const createConnection = (config, name) => {
  const sequelize = new Sequelize({
    host: config.host,
    port: config.port,
    username: config.username,
    password: config.password,
    database: config.database,
    dialect: 'postgres',
    logging: (msg) => logger.debug(`[${name}] ${msg}`),
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    },
    retry: {
      max: 3,
      match: [
        Sequelize.ConnectionError,
        Sequelize.ConnectionTimedOutError,
        Sequelize.TimeoutError
      ]
    }
  });

  return sequelize;
};

const initializeDatabase = async () => {
  try {
    // Master database configuration
    const masterConfig = {
      host: process.env.DB_MASTER_HOST,
      port: process.env.DB_MASTER_PORT,
      username: process.env.DB_MASTER_USERNAME,
      password: process.env.DB_MASTER_PASSWORD,
      database: process.env.DB_MASTER_DATABASE
    };

    // Replica database configuration
    const replicaConfig = {
      host: process.env.DB_REPLICA_HOST,
      port: process.env.DB_REPLICA_PORT,
      username: process.env.DB_REPLICA_USERNAME,
      password: process.env.DB_REPLICA_PASSWORD,
      database: process.env.DB_REPLICA_DATABASE
    };

    // Create connections
    masterDb = createConnection(masterConfig, 'MASTER');
    replicaDb = createConnection(replicaConfig, 'REPLICA');

    // Test connections
    await masterDb.authenticate();
    logger.info('✅ Master database connected');

    await replicaDb.authenticate();
    logger.info('✅ Replica database connected');

    // Sync models (only on master in development)
    if (process.env.NODE_ENV === 'development') {
      // Force sync to create tables
      await masterDb.sync({ force: false, alter: true });
      logger.info('✅ Database models synchronized');
    }

  } catch (error) {
    logger.error('❌ Database connection failed:', error);
    throw error;
  }
};

const getDatabase = (operation = 'read') => {
  const writeOperations = ['create', 'update', 'delete', 'upsert', 'bulkCreate', 'bulkUpdate', 'bulkDelete', 'write'];
  const isWrite = writeOperations.includes(operation);

  return isWrite ? masterDb : replicaDb;
};

const transaction = async (callback) => {
  const t = await masterDb.transaction();

  try {
    const result = await callback(t);
    await t.commit();
    return result;
  } catch (error) {
    await t.rollback();
    throw error;
  }
};

module.exports = {
  initializeDatabase,
  getDatabase,
  transaction,
  masterDb,
  replicaDb
};