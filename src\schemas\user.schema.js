const Joi = require('joi');

const schemas = {
  userCreate: Joi.object({
    email: Joi.string()
      .email()
      .required()
      .lowercase()
      .trim()
      .messages({
        'string.email': 'Please provide a valid email address',
        'any.required': 'Email is required'
      }),
    
    // username: Joi.string()
    //   .alphanum()
    //   .min(3)
    //   .max(30)
    //   .required()
    //   .lowercase()
    //   .trim()
    //   .messages({
    //     'string.alphanum': 'Username must contain only letters and numbers',
    //     'string.min': 'Username must be at least 3 characters long',
    //     'string.max': 'Username cannot exceed 30 characters',
    //     'any.required': 'Username is required'
    //   }),
    
    password: Joi.string()
      .min(8)
      .max(128)
      .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])'))
      .required()
      .messages({
        'string.min': 'Password must be at least 8 characters long',
        'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
        'any.required': 'Password is required'
      }),
    
    fullName: Joi.string()
      .min(4)
      .max(100)
      .trim()
      .messages({
        'string.min': 'Full name must be at least 4 characters long',
        'string.max': 'Full name cannot exceed 100 characters'
      }),
    
    role: Joi.string()
      .valid('user', 'admin', 'moderator')
      .default('user'),
    
    phone: Joi.string()
      .pattern(/^\+?[1-9]\d{1,14}$/)
      .messages({
        'string.pattern.base': 'Please provide a valid phone number'
      }),
    
  //   dateOfBirth: Joi.date()
  //     .max('now')
  //     .messages({
  //       'date.max': 'Date of birth cannot be in the future'
  //     }),
    
  //   address: Joi.object({
  //     street: Joi.string().required(),
  //     city: Joi.string().required(),
  //     state: Joi.string().required(),
  //     country: Joi.string().required(),
  //     postalCode: Joi.string().required()
  //   })
  }),

  userUpdate: Joi.object({
    email: Joi.string()
      .email()
      .lowercase()
      .trim()
      .messages({
        'string.email': 'Please provide a valid email address'
      }),
    
    // username: Joi.string()
    //   .alphanum()
    //   .min(3)
    //   .max(30)
    //   .lowercase()
    //   .trim()
    //   .messages({
    //     'string.alphanum': 'Username must contain only letters and numbers',
    //     'string.min': 'Username must be at least 3 characters long',
    //     'string.max': 'Username cannot exceed 30 characters'
    //   }),
    
    fullName: Joi.string()
      .min(4)
      .max(100)
      .trim()
      .messages({
        'string.min': 'Full name must be at least 4 characters long',
        'string.max': 'Full name cannot exceed 100 characters'
      }),
    
    phone: Joi.string()
      .pattern(/^(?:\+92|0)3\d{9}$/)
      .messages({
        'string.pattern.base': 'Please provide a valid Pakistani mobile number (03001234567 or +923001234567)'
      }),
    
    cnic: Joi.string()
      .pattern(/^(\d{5}-\d{7}-\d{1}|\d{13})$/)
      .messages({
        'string.pattern.base': 'Please provide a valid CNIC number (e.g., 12345-1234567-1 or 1234512345671)'
  }),

    
    // dateOfBirth: Joi.date()
    //   .max('now')
    //   .messages({
    //     'date.max': 'Date of birth cannot be in the future'
    //   }),
    
    // address: Joi.object({
    //   street: Joi.string().required(),
    //   city: Joi.string().required(),
    //   state: Joi.string().required(),
    //   country: Joi.string().required(),
    //   postalCode: Joi.string().required()
    // }),
    
    status: Joi.string()
      .valid('active', 'inactive', 'suspended')
      .messages({
        'any.only': 'Invalid status value'
      })
  }).min(1),

  passwordChange: Joi.object({
    currentPassword: Joi.string()
      .required()
      .messages({
        'any.required': 'Current password is required'
      }),
    
    newPassword: Joi.string()
      .min(8)
      .max(128)
      .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])'))
      .required()
      .invalid(Joi.ref('currentPassword'))
      .messages({
        'string.min': 'Password must be at least 8 characters long',
        'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
        'any.required': 'New password is required',
        'any.invalid': 'New password must be different from current password'
      }),
    
    confirmPassword: Joi.string()
      .required()
      .valid(Joi.ref('newPassword'))
      .messages({
        'any.required': 'Password confirmation is required',
        'any.only': 'Passwords do not match'
      })
  }),

  passwordReset: Joi.object({
    password: Joi.string()
      .min(8)
      .max(128)
      .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])'))
      .required()
      .messages({
        'string.min': 'Password must be at least 8 characters long',
        'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
        'any.required': 'Password is required'
      })
  }),

  userPreferences: Joi.object({
    preferences: Joi.object({
      notifications: Joi.object({
        email: Joi.boolean(),
        sms: Joi.boolean(),
        push: Joi.boolean()
      }),
      language: Joi.string().valid('en', 'es', 'fr', 'de'),
      timezone: Joi.string(),
      theme: Joi.string().valid('light', 'dark', 'auto'),
      marketing: Joi.object({
        newsletters: Joi.boolean(),
        promotions: Joi.boolean(),
        surveys: Joi.boolean()
      })
    }).required()
  }),

  login: Joi.object({
    email: Joi.string()
      .email()
      .required()
      .lowercase()
      .trim()
      .messages({
        'string.email': 'Please provide a valid email address',
        'any.required': 'Email is required'
      }),
    
    password: Joi.string()
      .required()
      .messages({
        'any.required': 'Password is required'
      })
  }),

  refreshToken: Joi.object({
    refreshToken: Joi.string()
      .required()
      .messages({
        'any.required': 'Refresh token is required'
      })
  })
};

module.exports = schemas;