const { logger } = require('../utils/logger');

/**
 * Dependency Injection Container
 * Manages registration and resolution of dependencies
 */
const createContainer = () => {
  const dependencies = new Map();
  const singletons = new Map();

  return {
    /**
     * Register a dependency
     * @param {string} name - Dependency name
     * @param {*} factory - Factory function or value
     * @param {object} options - Registration options
     */
    register(name, factory, options = {}) {
      if (!name || typeof name !== 'string') {
        throw new Error('Dependency name must be a non-empty string');
      }

      dependencies.set(name, {
        factory,
        options: {
          singleton: false,
          ...options
        }
      });
      
      logger.debug(`✓ Registered dependency: ${name}${options.singleton ? ' (singleton)' : ''}`);
    },

    /**
     * Register multiple dependencies at once
     * @param {object} deps - Object with name-value pairs
     * @param {object} options - Default options for all dependencies
     */
    registerBatch(deps, options = {}) {
      Object.entries(deps).forEach(([name, factory]) => {
        this.register(name, factory, options);
      });
    },

    /**
     * Resolve a dependency by name or all dependencies
     * @param {string} [name] - Dependency name (optional)
     * @returns {*} Resolved dependency or all dependencies
     */
    resolve(name) {
      if (!name) {
        return this.resolveAll();
      }

      if (!dependencies.has(name)) {
        throw new Error(`Dependency '${name}' not found. Available: ${this.list().join(', ')}`);
      }

      const { factory, options } = dependencies.get(name);

      // Handle singletons
      if (options.singleton) {
        if (!singletons.has(name)) {
          const instance = this.createInstance(factory, name);
          singletons.set(name, instance);
          logger.debug(`✓ Created singleton instance: ${name}`);
        }
        return singletons.get(name);
      }

      // Return factory or create new instance
      return this.createInstance(factory, name);
    },

    /**
     * Resolve all dependencies
     * @returns {object} All resolved dependencies
     */
    resolveAll() {
      const resolved = {};
      for (const [key] of dependencies) {
        try {
          resolved[key] = this.resolve(key);
        } catch (error) {
          logger.warn(`Failed to resolve dependency '${key}': ${error.message}`);
        }
      }
      return resolved;
    },

    /**
     * Create instance from factory
     * @private
     */
    createInstance(factory, name) {
      try {
        if (typeof factory === 'function') {
          // Pass container to factory for dependency injection
          return factory(this);
        }
        return factory;
      } catch (error) {
        logger.error(`Failed to create instance for '${name}': ${error.message}`);
        throw error;
      }
    },

    /**
     * Check if dependency exists
     * @param {string} name - Dependency name
     * @returns {boolean}
     */
    has(name) {
      return dependencies.has(name);
    },

    /**
     * Get list of registered dependency names
     * @returns {string[]}
     */
    list() {
      return Array.from(dependencies.keys());
    },

    /**
     * Get dependency count
     * @returns {number}
     */
    size() {
      return dependencies.size;
    },

    /**
     * Remove a dependency
     * @param {string} name - Dependency name
     */
    remove(name) {
      dependencies.delete(name);
      singletons.delete(name);
      logger.debug(`✓ Removed dependency: ${name}`);
    },

    /**
     * Clear all dependencies
     */
    clear() {
      const count = dependencies.size;
      dependencies.clear();
      singletons.clear();
      logger.debug(`✓ Cleared ${count} dependencies`);
    },

    /**
     * Get container statistics
     * @returns {object}
     */
    getStats() {
      return {
        totalDependencies: dependencies.size,
        singletonInstances: singletons.size,
        dependencies: this.list()
      };
    }
  };
};

module.exports = { createContainer };

// const { logger } = require('../utils/logger');

// const createContainer = () => {
//   const dependencies = new Map();
//   const singletons = new Map();

//   return {
//     register(name, factory, options = {}) {
//       dependencies.set(name, {
//         factory,
//         options
//       });
      
//       logger.debug(`Registered dependency: ${name}`);
//     },

//     resolve(name) {
//       if (!name) {
//         // Return all dependencies
//         const resolved = {};
//         for (const [key, value] of dependencies) {
//           if (typeof value.factory === 'function') {
//             resolved[key] = value.factory;
//           } else {
//             resolved[key] = value.factory;
//           }
//         }
//         return resolved;
//       }

//       if (!dependencies.has(name)) {
//         throw new Error(`Dependency '${name}' not found`);
//       }

//       const { factory, options } = dependencies.get(name);

//       // Handle singletons
//       if (options.singleton) {
//         if (!singletons.has(name)) {
//           const instance = typeof factory === 'function' ? factory(this) : factory;
//           singletons.set(name, instance);
//         }
//         return singletons.get(name);
//       }

//       // Return factory or value
//       return typeof factory === 'function' ? factory(this) : factory;
//     },

//     has(name) {
//       return dependencies.has(name);
//     },

//     list() {
//       return Array.from(dependencies.keys());
//     },

//     clear() {
//       dependencies.clear();
//       singletons.clear();
//     }
//   };
// };

// module.exports = { createContainer };