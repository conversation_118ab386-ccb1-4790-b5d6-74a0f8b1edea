const grpc = require('@grpc/grpc-js');
const protoLoader = require('@grpc/proto-loader');
const path = require('path');
const { logger } = require('../../utils/logger');
const { serviceRegistry } = require('./service-registry');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const createGrpcHandlers = require('./handlers');
const withAuth = require('./middleware');

const PROTO_PATH = path.join(__dirname, 'protos', 'service.proto');

const packageDefinition = protoLoader.loadSync(PROTO_PATH, {
  keepCase: true,
  longs: String,
  enums: String,
  defaults: true,
  oneofs: true
});

const proto = grpc.loadPackageDefinition(packageDefinition);


const startGrpcServer = async (container, jwt) => {

  let server;

  try {

    server = new grpc.Server();

    const handlers = createGrpcHandlers(container, jwt);

    // Add services with registry
    server.addService(proto.app.AuthService.service, {
      Register: handlers.CreateUser,
      Login: handlers.Login
    })

    server.addService(proto.app.UserService.service, {
      GetUser: withAuth(handlers.GetUser, jwt, container),
      UpdateUser: withAuth(handlers.UpdateUser, jwt, container),
      DeleteUser: withAuth(handlers.DeleteUser, jwt, container),
      ListUsers: withAuth(handlers.ListUsers, jwt, container),
    });

    // Register services in service registry
    serviceRegistry.registerService('app.UserService', proto.app.UserService, PROTO_PATH);
    serviceRegistry.registerService('app.AuthService', proto.app.AuthService, PROTO_PATH);

    // Enable server reflection - Correct implementation
    const { ReflectionService } = require('@grpc/reflection');

    // Create reflection service with the loaded package definition
    const reflection = new ReflectionService(packageDefinition);
    reflection.addToServer(server);

    logger.info('✅ gRPC server reflection enabled successfully');
  } catch (reflectionError) {
    console.error('reflection error', reflectionError)
    logger.warn('⚠️  gRPC server reflection setup failed:', reflectionError.message);
    logger.info('💡 You can still test gRPC services using proto files directly');
  }

  // Start server
  const port = process.env.GRPC_PORT || 50051;
  const grpcDir = './src/edge/grpc/';

  let serverCredentials;

  try {
    serverCredentials = grpc.ServerCredentials.createSsl(
      fs.readFileSync(`${grpcDir}/certs/ca.crt`),
      [{ private_key: fs.readFileSync(`${grpcDir}/certs/server.key`), cert_chain: fs.readFileSync(`${grpcDir}/certs/server.crt`) }],
      true // Request client certificate
    );

  } catch (error) {
    logger.error(`Failed to secure the server ${error} shifting to insecure`)
    serverCredentials = grpc.ServerCredentials.createInsecure();
  }

  return new Promise((resolve) => {
    server.bindAsync(
      `0.0.0.0:${port}`,
      serverCredentials,
      (error, boundPort) => {
        if (error) {
          logger.error('Failed to start gRPC server:', error);
          logger.warn('Continuing without gRPC so HTTP API can start');
          return resolve(null);
        }

        server.start();
        logger.info(`✅ gRPC server running on port ${boundPort}`);
        logger.info(`🔍 Available services: app.UserService, app.OrderService`);
        logger.info(`💡 Test with: grpcurl -plaintext localhost:${boundPort} list`);

        // Print service registry info
        serviceRegistry.printServiceInfo();

        // Export service discovery
        const discoveryPath = path.join(__dirname, '../../docs/grpc-services.json');
        try {
          serviceRegistry.exportServiceDiscovery(discoveryPath);
        } catch (err) {
          logger.warn('Could not export service discovery:', err.message);
        }

        resolve(server);
      }
    );
  });
};

// Service discovery helper - returns available services info
const getServiceInfo = () => {
  return {
    services: [
      {
        name: 'app.UserService',
        methods: ['GetUser', 'DeleteUesr', 'UpdateUser', 'ListUsers'],
        description: 'User management service'
      },
      {
        name: 'app.AuthService',
        methods: ['Login', 'Register'],
        description: 'Order management service with streaming support'
      }
    ],
    proto_file: 'service.proto',
    package: 'app'
  };
};

module.exports = {
  startGrpcServer,
  getServiceInfo
};