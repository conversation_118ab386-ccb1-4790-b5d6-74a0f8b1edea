services:
  # PostgreSQL (Primary only)
  postgres:
    platform: linux/amd64
  # PostgreSQL (Primary only)
  postgres:
    platform: linux/amd64
    image: postgres:16
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: 1234
      POSTGRES_DB: conclavity_db
    ports:
      - "8888:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init-primary.sh:/docker-entrypoint-initdb.d/init-primary.sh
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U user -d conclavity_db" ]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 40s

  # PostgreSQL Exporter
  postgres-exporter:
    platform: linux/amd64
    image: prometheuscommunity/postgres-exporter:latest
    environment:
      DATA_SOURCE_NAME: "************************************/app_db?sslmode=disable"
    ports:
      - "9187:9187"
    depends_on:
      postgres:
        condition: service_healthy

  # Redis (single instance)
  redis:
    platform: linux/amd64
  # Redis (single instance)
  redis:
    platform: linux/amd64
    image: redis:7-alpine
    ports:
      - "6379:6379"
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Exporter
  redis-exporter:
    platform: linux/amd64
      - "6379:6379"
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Exporter
  redis-exporter:
    platform: linux/amd64
    image: oliver006/redis_exporter:latest
    environment:
      REDIS_ADDR: redis:6379
      REDIS_ADDR: redis:6379
    ports:
      - "9121:9121"
    depends_on:
      redis:
        condition: service_healthy

  # RabbitMQ
  rabbitmq:
    platform: linux/amd64
    platform: linux/amd64
    image: rabbitmq:3-management
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin
    healthcheck:
      test: [ "CMD", "rabbitmq-diagnostics", "ping" ]
      interval: 15s
      timeout: 10s
      retries: 5

  # RabbitMQ Exporter
  rabbitmq-exporter:
    platform: linux/amd64
    image: kbudde/rabbitmq-exporter:latest
    ports:
      - "15692:15692"
    environment:
      RABBIT_URL: *********************************
    depends_on:
      rabbitmq:
        condition: service_healthy
    healthcheck:
      test: [ "CMD", "rabbitmq-diagnostics", "ping" ]
      interval: 15s
      timeout: 10s
      retries: 5

  # RabbitMQ Exporter
  rabbitmq-exporter:
    platform: linux/amd64
    image: kbudde/rabbitmq-exporter:latest
    ports:
      - "15692:15692"
    environment:
      RABBIT_URL: *********************************
    depends_on:
      rabbitmq:
        condition: service_healthy

  # Zookeeper
  zookeeper:
    image: confluentinc/cp-zookeeper:7.5.0
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    healthcheck:
      # test: ["CMD", "zkCli.sh", "ls", "/"]
      test: [ "CMD", "bash", "-c", "/opt/bitnami/zookeeper/bin/zkServer.sh status | grep Mode" ]
      interval: 15s
      timeout: 10s
      retries: 5
    healthcheck:
      # test: ["CMD", "zkCli.sh", "ls", "/"]
      test: [ "CMD", "bash", "-c", "/opt/bitnami/zookeeper/bin/zkServer.sh status | grep Mode" ]
      interval: 15s
      timeout: 10s
      retries: 5

  # Zookeeper Exporter
  zookeeper-exporter:
    image: dabealu/zookeeper-exporter:latest
    environment:
      ZK_HOSTS: zookeeper:2181
    ports:
      - "9141:9141"
    depends_on:
      zookeeper:
        condition: service_healthy

  # Kafka
  kafka:
    image: confluentinc/cp-kafka:7.5.0
    depends_on:
      zookeeper:
        condition: service_healthy
    ports:
      - "9092:9092"
      - "9093:9093" # External listener
      - "7070:7070"
      - "9093:9093" # External listener
      - "7070:7070"
    environment:
            KAFKA_BROKER_ID: 1
            KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
            KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
            KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:9092
            KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
            KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"

  # Kafka Exporter
  kafka-exporter:
    platform: linux/amd64
    image: danielqsj/kafka-exporter:latest
    command: --kafka.server=kafka:9092
    ports:
      - "9308:9308"
    depends_on:
      kafka:
        condition: service_healthy

  # MQTT Broker
  mosquitto:
    platform: linux/amd64
    image: eclipse-mosquitto:latest
    ports:
      - "1883:1883"
      - "9001:9001"
    volumes:
      - ./infrastructure/mosquitto.conf:/mosquitto/config/mosquitto.conf
      - mosquitto-data:/mosquitto/data
    healthcheck:
      test: [ "CMD", "mosquitto_pub", "-h", "localhost", "-t", "test", "-m", "health_check" ]
      interval: 15s
      timeout: 5s
      retries: 5

  # MQTT Metrics Publisher
  mqtt-metrics-publisher:
    build:
      context: .
      dockerfile: Dockerfile.mqtt-publisher
    platform: linux/amd64
    depends_on:
      mosquitto:
        condition: service_healthy

  # MQTT Exporter
  mqtt-exporter:
    image: sapcc/mosquitto-exporter:latest
    environment:
      MQTT_BROKER: tcp://mosquitto:1883
      MQTT_TOPIC: metrics/#
    ports:
      - "9641:9641"
    depends_on:
      mosquitto:
        condition: service_healthy
      mqtt-metrics-publisher:
        condition: service_started

  # Prometheus
  prometheus:
    platform: linux/amd64
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./infrastructure/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=15d'
      - '--web.enable-lifecycle'
    depends_on:
      - postgres-exporter
      - redis-exporter
      - rabbitmq-exporter
      - kafka-exporter
      - mqtt-exporter
      - zookeeper-exporter

  # Grafana
  grafana:
    platform: linux/amd64
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-data:/var/lib/grafana
    depends_on:
      - prometheus

  # Adminer
  adminer:
    image: adminer
    restart: always
    ports:
      - "8080:8080"

volumes:
  postgres-data:
  mosquitto-data:
  kafka-data:
  prometheus-data:
  grafana-data:
