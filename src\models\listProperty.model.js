// models/property.model.js
const { DataTypes } = require('sequelize');

const definePropertyModel = (sequelize) => {
  const Property = sequelize.define('Property', {
    PropertyID: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    ListingID: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'listings',
        key: 'ListingID'
      }
    },
    PropertyType: { type: DataTypes.STRING, allowNull: false },
    //Address: { type: DataTypes.STRING, allowNull: false },
    AreaSize: { type: DataTypes.DECIMAL(10, 2), allowNull: false },
    NumberOfBedrooms: { type: DataTypes.INTEGER, allowNull: false },
    NumberOfBathrooms: { type: DataTypes.INTEGER, allowNull: false },
    Furnished: { type: DataTypes.BOOLEAN, allowNull: false },
    FloorNumber: { type: DataTypes.INTEGER, allowNull: true },
    ParkingAvailable: { type: DataTypes.BOOLEAN, allowNull: true },
    //AvailableFrom: { type: DataTypes.DATE, allowNull: true },
    //MinimumRentalPeriod: { type: DataTypes.INTEGER, allowNull: true }
  }, {
    tableName: 'properties',
    timestamps: false
  });

  Property.associate = (models) => {
    Property.belongsTo(models.Listing, {
      foreignKey: 'ListingID',
      as: 'Listing'
    });
  };

  return Property;
};

module.exports = definePropertyModel;
