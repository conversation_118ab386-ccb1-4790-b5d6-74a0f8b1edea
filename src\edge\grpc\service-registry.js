const fs = require('fs');
const path = require('path');
const { logger } = require('../../utils/logger');

class GrpcServiceRegistry {
  constructor() {
    this.services = new Map();
    this.protoFiles = new Map();
    this.serverInfo = {
      host: 'localhost',
      port: process.env.GRPC_PORT || 50051,
      startTime: new Date(),
      version: '1.0.0'
    };
  }

  // Register a service with its metadata
  registerService(serviceName, serviceDefinition, protoPath) {
    const serviceInfo = {
      name: serviceName,
      methods: this.extractMethods(serviceDefinition),
      protoPath,
      registeredAt: new Date(),
      status: 'active'
    };

    this.services.set(serviceName, serviceInfo);
    
    if (protoPath) {
      this.protoFiles.set(serviceName, protoPath);
    }

    logger.info(`📝 Registered gRPC service: ${serviceName}`);
    return serviceInfo;
  }

  // Extract method information from service definition
  extractMethods(serviceDefinition) {
    const methods = [];
    
    if (serviceDefinition && serviceDefinition.service) {
      for (const [methodName, methodDef] of Object.entries(serviceDefinition.service)) {
        methods.push({
          name: methodName,
          requestType: methodDef.requestType?.type?.name || 'Unknown',
          responseType: methodDef.responseType?.type?.name || 'Unknown',
          requestStream: methodDef.requestStream || false,
          responseStream: methodDef.responseStream || false,
          path: methodDef.path || `/${serviceDefinition.service.serviceName}/${methodName}`
        });
      }
    }

    return methods;
  }

  // Get all registered services
  getAllServices() {
    return Array.from(this.services.values());
  }

  // Get specific service info
  getService(serviceName) {
    return this.services.get(serviceName);
  }

  // Get server health info
  getServerInfo() {
    return {
      ...this.serverInfo,
      uptime: Date.now() - this.serverInfo.startTime.getTime(),
      serviceCount: this.services.size,
      services: Array.from(this.services.keys())
    };
  }

  // Generate service discovery JSON
  generateServiceDiscovery() {
    return {
      server: this.getServerInfo(),
      services: this.getAllServices(),
      reflection: {
        enabled: true,
        endpoint: `${this.serverInfo.host}:${this.serverInfo.port}`
      },
      testing: {
        grpcurl: {
          list_services: `grpcurl -plaintext ${this.serverInfo.host}:${this.serverInfo.port} list`,
          example_call: `grpcurl -plaintext -d '{}' ${this.serverInfo.host}:${this.serverInfo.port} app.UserService/ListUsers`
        },
        proto_files: Array.from(this.protoFiles.values())
      }
    };
  }

  // Export service discovery to file
  exportServiceDiscovery(outputPath) {
    const discovery = this.generateServiceDiscovery();
    const json = JSON.stringify(discovery, null, 2);
    
    fs.writeFileSync(outputPath, json);
    logger.info(`📋 Service discovery exported to: ${outputPath}`);
    
    return discovery;
  }

  // Generate OpenAPI-style documentation for gRPC services
  generateApiDocs() {
    const docs = {
      openapi: '3.0.0',
      info: {
        title: 'gRPC API Documentation',
        version: this.serverInfo.version,
        description: 'Auto-generated documentation for gRPC services'
      },
      servers: [
        {
          url: `grpc://${this.serverInfo.host}:${this.serverInfo.port}`,
          description: 'gRPC Server'
        }
      ],
      paths: {}
    };

    // Convert gRPC services to OpenAPI-style paths
    for (const service of this.services.values()) {
      for (const method of service.methods) {
        const path = `/grpc${method.path}`;
        docs.paths[path] = {
          post: {
            summary: `${service.name}.${method.name}`,
            description: `gRPC method ${method.name} in service ${service.name}`,
            tags: [service.name],
            requestBody: {
              content: {
                'application/grpc': {
                  schema: {
                    type: 'object',
                    description: method.requestType
                  }
                }
              }
            },
            responses: {
              '200': {
                description: 'Successful response',
                content: {
                  'application/grpc': {
                    schema: {
                      type: 'object',
                      description: method.responseType
                    }
                  }
                }
              }
            }
          }
        };
      }
    }

    return docs;
  }

  // Generate client code snippets
  generateClientExamples(language = 'javascript') {
    const examples = {};

    for (const service of this.services.values()) {
      examples[service.name] = {};

      for (const method of service.methods) {
        if (language === 'javascript') {
          examples[service.name][method.name] = `
// ${service.name}.${method.name}
const client = new proto.${service.name.replace('app.', '')}('localhost:${this.serverInfo.port}', grpc.credentials.createInsecure());

${method.requestStream || method.responseStream ? 
  this.generateStreamingExample(service.name, method) : 
  this.generateUnaryExample(service.name, method)
}`;
        }
      }
    }

    return examples;
  }

  generateUnaryExample(serviceName, method) {
    return `client.${method.name}({
  // Add your request parameters here
}, (error, response) => {
  if (error) {
    console.error('Error:', error);
  } else {
    console.log('Response:', response);
  }
});`;
  }

  generateStreamingExample(serviceName, method) {
    if (method.requestStream && method.responseStream) {
      return `const stream = client.${method.name}();
stream.on('data', (response) => {
  console.log('Received:', response);
});
stream.write({ /* request data */ });
stream.end();`;
    } else if (method.responseStream) {
      return `const stream = client.${method.name}({ /* request data */ });
stream.on('data', (response) => {
  console.log('Received:', response);
});
stream.on('end', () => {
  console.log('Stream ended');
});`;
    } else if (method.requestStream) {
      return `const stream = client.${method.name}((error, response) => {
  if (error) console.error('Error:', error);
  else console.log('Response:', response);
});
stream.write({ /* request data */ });
stream.end();`;
    }
  }

  // Print service registry info to console
  printServiceInfo() {
    console.log('\n🗺️  gRPC Service Registry');
    console.log('=' .repeat(50));
    console.log(`Server: ${this.serverInfo.host}:${this.serverInfo.port}`);
    console.log(`Uptime: ${Math.round((Date.now() - this.serverInfo.startTime.getTime()) / 1000)}s`);
    console.log(`Services: ${this.services.size}`);
    console.log('');

    for (const service of this.services.values()) {
      console.log(`📦 ${service.name}`);
      console.log(`   Proto: ${service.protoPath || 'N/A'}`);
      console.log(`   Methods (${service.methods.length}):`);
      
      for (const method of service.methods) {
        const streamInfo = method.requestStream || method.responseStream ? 
          ` (${method.requestStream ? 'req-stream' : ''}${method.requestStream && method.responseStream ? ',' : ''}${method.responseStream ? 'resp-stream' : ''})` : '';
        console.log(`     - ${method.name}${streamInfo}`);
      }
      console.log('');
    }

    console.log('Testing Commands:');
    console.log(`  grpcurl -plaintext ${this.serverInfo.host}:${this.serverInfo.port} list`);
    console.log(`  grpcurl -plaintext -d '{}' ${this.serverInfo.host}:${this.serverInfo.port} app.UserService/ListUsers`);
    console.log('');
  }
}

// Global registry instance
const serviceRegistry = new GrpcServiceRegistry();

module.exports = {
  GrpcServiceRegistry,
  serviceRegistry
};