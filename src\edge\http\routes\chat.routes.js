const { authHook } = require('../../../hooks/auth.hook');
const chatController = require('../controllers/chat.controller');

const chatRoutes = async (fastify, options) => {

    // Get user's conversations
    fastify.get('/conversations', {
        preHandlers: [authHook],
        schema: {
            description: 'Get user conversations',
            tags: ['Chat'],
            security: [{ bearerAuth: [] }],
            response: {
                200: {
                    description: 'List of conversations',
                    type: 'object',
                    properties: {
                        success: { type: 'boolean' },
                        conversations: { type: 'array' },
                        pagination: { type: 'object' }
                    }
                }
            }
        }
    }, chatController.getUserConversations);

    // Get specific conversation with messages
    fastify.get('/conversations/:conversationId', {
        preHandlers: [authHook],
        schema: {
            description: 'Get conversation details',
            tags: ['Chat'],
            security: [{ bearerAuth: [] }],
            response: {
                200: {
                    description: 'Conversation with messages',
                    type: 'object',
                    properties: {
                        success: { type: 'boolean' },
                        conversation: { type: 'object' },
                        messages: { type: 'array' }
                    }
                }
            }
        }
    }, chatController.getConversation);

    // Create or get conversation
    // fastify.post('/conversations', {
    //     preHandlers: [authHook],
    //     schema: {
    //         description: 'Create new conversation',
    //         tags: ['Chat'],
    //         security: [{ bearerAuth: [] }],
    //         response: {
    //             201: {
    //                 description: 'Created conversation',
    //                 type: 'object',
    //                 properties: {
    //                     success: { type: 'boolean' },
    //                     conversation: { type: 'object' }
    //                 }
    //             }
    //         }
    //     }
    // }, chatController.createConversation);

    // // Send message via REST (alternative to socket)
    // fastify.post('/conversations/:conversationId/messages', {
    //     preHandlers: [authHook],
    //     schema: {
    //         description: 'Send message in conversation',
    //         tags: ['Chat'],
    //         security: [{ bearerAuth: [] }],
    //         response: {
    //             201: {
    //                 description: 'Message sent successfully',
    //                 type: 'object',
    //                 properties: {
    //                     success: { type: 'boolean' },
    //                     message: { type: 'object' }
    //                 }
    //             }
    //         }
    //     }
    // }, chatController.sendMessage);

    // // Mark message as read
    // fastify.patch('/messages/:messageId/read', {
    //     preHandlers: [authHook],
    //     schema: {
    //         description: 'Mark message as read',
    //         tags: ['Chat'],
    //         security: [{ bearerAuth: [] }],
    //         response: {
    //             200: {
    //                 description: 'Message marked as read',
    //                 type: 'object',
    //                 properties: {
    //                     success: { type: 'boolean' },
    //                     message: { type: 'string' }
    //                 }
    //             }
    //         }
    //     }
    // }, chatController.markMessageAsRead);

    // Get chat statistics
    fastify.get('/stats', {
        preHandlers: [authHook],
        schema: {
            description: 'Get chat statistics',
            tags: ['Chat'],
            security: [{ bearerAuth: [] }],
            response: {
                200: {
                    description: 'Chat statistics',
                    type: 'object',
                    properties: {
                        success: { type: 'boolean' },
                        stats: { type: 'object' }
                    }
                }
            }
        }
    }, chatController.getChatStats);
};


// const createChatRoutes = (container) => {
//     const chatController = container.resolve('chatController');

//     // Get user's conversations
//     fastify.get('/conversations', chatController.getUserConversations.bind(chatController));

//     // Get specific conversation with messages
//     fastify.get('/conversations/:conversationId', chatController.getConversation.bind(chatController));

//     // Create or get conversation
//     fastify.post('/conversations', chatController.createConversation.bind(chatController));

//     // Send message via REST (alternative to socket)
//     fastify.post('/conversations/:conversationId/messages', chatController.sendMessage.bind(chatController));

//     // Mark message as read
//     fastify.patch('/messages/:messageId/read', chatController.markMessageAsRead.bind(chatController));

//     // Get chat statistics
//     fastify.get('/stats', chatController.getChatStats.bind(chatController));
// };

module.exports = chatRoutes;
