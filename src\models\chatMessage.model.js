const { DataTypes } = require('sequelize');

const defineChatMessageModel = (sequelize) => {
  const ChatMessage = sequelize.define('ChatMessage', {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      allowNull: false
    },
    conversationId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'chat_conversations',
        key: 'id'
      }
    },
    senderId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    recipientId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    type: {
      type: DataTypes.ENUM('text', 'image', 'file'),
      defaultValue: 'text',
      allowNull: false
    },
    isRead: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    },
    readAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Store additional message metadata like file info, image dimensions, etc.'
    }
  }, {
    tableName: 'chat_messages',
    timestamps: true,
    indexes: [
      {
        fields: ['conversationId']
      },
      {
        fields: ['senderId']
      },
      {
        fields: ['recipientId']
      },
      {
        fields: ['createdAt']
      },
      {
        fields: ['conversationId', 'createdAt']
      }
    ]
  });

  ChatMessage.associate = (models) => {
    // Message belongs to a conversation
    ChatMessage.belongsTo(models.ChatConversation, {
      foreignKey: 'conversationId',
      as: 'conversation'
    });

    // Message belongs to sender
    ChatMessage.belongsTo(models.User, {
      foreignKey: 'senderId',
      as: 'sender'
    });

    // Message belongs to recipient
    ChatMessage.belongsTo(models.User, {
      foreignKey: 'recipientId',
      as: 'recipient'
    });
  };

  return ChatMessage;
};

module.exports = defineChatMessageModel;
