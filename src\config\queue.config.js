const amqp = require('amqplib');
// const { Kafka } = require('kafkajs');
const { logger } = require('../utils/logger');

let rabbitConnection = null;
let rabbitChannel = null;
// let kafkaProducer = null;
// let kafkaConsumer = null;
// let kafkaAdmin = null;

const initializeQueues = async () => {
  await initializeRabbitMQ();
  // await initializeKafka();
};

const initializeRabbitMQ = async () => {
  try {
    // Connect to RabbitMQ
    rabbitConnection = await amqp.connect(process.env.RABBITMQ_URL || 'amqp://localhost');
    rabbitChannel = await rabbitConnection.createChannel();

    // Set prefetch for fair dispatch
    await rabbitChannel.prefetch(1);

    // Create exchanges
    await rabbitChannel.assertExchange('events', 'topic', { durable: true });
    await rabbitChannel.assertExchange('dlx', 'topic', { durable: true });

    // Create queues
    const queues = [
      { name: 'user.events', routingKey: 'user.*' },
      { name: 'order.events', routingKey: 'order.*' },
      { name: 'notification.events', routingKey: 'notification.*' }
    ];

    for (const queue of queues) {
      await rabbitChannel.assertQueue(queue.name, {
        durable: true,
        arguments: {
          'x-dead-letter-exchange': 'dlx',
          'x-message-ttl': 3600000 // 1 hour
        }
      });
      await rabbitChannel.bindQueue(queue.name, 'events', queue.routingKey);
    }

    // Create dead letter queue
    await rabbitChannel.assertQueue('dlq', { durable: true });
    await rabbitChannel.bindQueue('dlq', 'dlx', '#');

    logger.info('✅ RabbitMQ connected and configured');

    // Handle connection events
    rabbitConnection.on('error', (err) => {
      logger.error('RabbitMQ connection error:', err);
    });

    rabbitConnection.on('close', () => {
      logger.warn('RabbitMQ connection closed');
    });

  } catch (error) {
    logger.error('❌ RabbitMQ initialization failed:', error);
    throw error;
  }
};

// const checkAndCreateKafkaTopics = async (admin, topics) => {
//   try {
//     logger.info('🔍 Checking existing Kafka topics...');

//     // List existing topics
//     const existingTopics = await admin.listTopics();
//     logger.info(`📋 Existing topics: ${existingTopics.join(', ')}`);

//     // Determine which topics need to be created
//     const topicsToCreate = topics.filter(topic => !existingTopics.includes(topic));

//     if (topicsToCreate.length > 0) {
//       logger.info(`🆕 Creating missing topics: ${topicsToCreate.join(', ')}`);

//       const topicConfigs = topicsToCreate.map(topic => ({
//         topic,
//         numPartitions: 3, // Configurable
//         replicationFactor: 1, // Should be 3 in production
//         configEntries: [
//           {
//             name: 'cleanup.policy',
//             value: 'delete'
//           },
//           {
//             name: 'retention.ms',
//             value: '604800000' // 7 days
//           }
//         ]
//       }));

//       const createResult = await admin.createTopics({
//         topics: topicConfigs,
//         waitForLeaders: true,
//         timeout: 30000
//       });

//       if (createResult) {
//         logger.info('✅ Topics created successfully');
//       }
//     } else {
//       logger.info('✅ All required topics already exist');
//     }

//     return existingTopics.concat(topicsToCreate);
//   } catch (error) {
//     logger.error('❌ Error managing Kafka topics:', error);
//     throw error;
//   }
// };

// const waitForKafkaHealth = async (admin, maxRetries = 10) => {
//   for (let i = 0; i < maxRetries; i++) {
//     try {
//       await admin.listTopics();
//       logger.info('✅ Kafka cluster is healthy');
//       return true;
//     } catch (error) {
//       logger.warn(`⏳ Waiting for Kafka cluster... (attempt ${i + 1}/${maxRetries})`);
//       await new Promise(resolve => setTimeout(resolve, 2000));
//     }
//   }
//   throw new Error('Kafka cluster health check failed');
// };

// const initializeKafka = async () => {
//   try {
//     const brokers = (process.env.KAFKA_BROKERS || 'localhost:9092').split(',').map(b => b.trim());
//     if (!brokers.length) {
//       throw new Error('No Kafka brokers specified in KAFKA_BROKERS');
//     }

//     logger.info(`🔌 Connecting to Kafka brokers: ${brokers.join(', ')}`);

//     const kafka = new Kafka({
//       clientId: process.env.KAFKA_CLIENT_ID || 'app-client',
//       brokers,
//       connectionTimeout: 15000,
//       requestTimeout: 30000,
//       retry: {
//         initialRetryTime: 3000,
//         retries: 8,
//         factor: 2,
//         multiplier: 1.5,
//         maxRetryTime: 30000,
//       },
//       logLevel: 4, // INFO level
//     });

//     // Create admin client for topic management
//     kafkaAdmin = kafka.admin();
//     await kafkaAdmin.connect();
//     logger.info('✅ Kafka admin client connected');

//     // Wait for Kafka to be fully ready
//     await waitForKafkaHealth(kafkaAdmin);

//     // Define required topics
//     const requiredTopics = ['user-events', 'order-events', 'system-events'];

//     // Check and create topics if they don't exist
//     await checkAndCreateKafkaTopics(kafkaAdmin, requiredTopics);

//     // Create producer
//     kafkaProducer = kafka.producer({
//       allowAutoTopicCreation: false, // We manage topics explicitly
//       transactionTimeout: 30000,
//       maxInFlightRequests: 5,
//       idempotent: true,
//       retry: {
//         initialRetryTime: 300,
//         retries: 5,
//         factor: 0.2,
//         multiplier: 2,
//         maxRetryTime: 30000,
//       }
//     });

//     await kafkaProducer.connect();
//     logger.info('✅ Kafka producer connected');

//     // Create consumer
//     kafkaConsumer = kafka.consumer({
//       groupId: process.env.KAFKA_GROUP_ID || 'app-group',
//       sessionTimeout: 30000,
//       heartbeatInterval: 3000,
//       maxWaitTimeInMs: 5000,
//       retry: {
//         initialRetryTime: 300,
//         retries: 5,
//         factor: 0.2,
//         multiplier: 2,
//         maxRetryTime: 30000,
//       }
//     });

//     await kafkaConsumer.connect();
//     logger.info('✅ Kafka consumer connected');

//     // Subscribe to topics with error handling
//     for (const topic of requiredTopics) {
//       try {
//         await kafkaConsumer.subscribe({
//           topic,
//           fromBeginning: false
//         });
//         logger.info(`✅ Subscribed to Kafka topic: ${topic}`);
//       } catch (error) {
//         logger.error(`❌ Failed to subscribe to topic ${topic}:`, error);
//         throw error;
//       }
//     }

//     // Set up event handlers
//     kafkaProducer.on('producer.connect', () => {
//       logger.info('🔄 Kafka producer reconnected');
//     });

//     kafkaProducer.on('producer.disconnect', () => {
//       logger.warn('⚠️ Kafka producer disconnected');
//     });

//     kafkaConsumer.on('consumer.connect', () => {
//       logger.info('🔄 Kafka consumer reconnected');
//     });

//     kafkaConsumer.on('consumer.disconnect', () => {
//       logger.warn('⚠️ Kafka consumer disconnected');
//     });

//     kafkaConsumer.on('consumer.crash', ({ payload }) => {
//       logger.error('💥 Kafka consumer crashed:', payload);
//     });

//     kafkaAdmin.on('admin.connect', () => {
//       logger.info('🔄 Kafka admin reconnected');
//     });

//     kafkaAdmin.on('admin.disconnect', () => {
//       logger.warn('⚠️ Kafka admin disconnected');
//     });

//   } catch (error) {
//     logger.error('❌ Kafka initialization failed:', error);
//     throw error;
//   }
// };

const publishToRabbit = async (exchange, routingKey, message) => {
  try {
    const buffer = Buffer.from(JSON.stringify(message));
    const result = await rabbitChannel.publish(exchange, routingKey, buffer, {
      persistent: true,
      timestamp: Date.now()
    });
    logger.info(`📤 Published message to RabbitMQ exchange ${exchange} with routing key ${routingKey}`);
    return result;
  } catch (error) {
    logger.error('❌ RabbitMQ publish error:', error);
    throw error;
  }
};

const consumeFromRabbit = async (queue, callback) => {
  try {
    await rabbitChannel.consume(queue, async (msg) => {
      if (msg) {
        try {
          const content = JSON.parse(msg.content.toString());
          await callback(content);
          rabbitChannel.ack(msg);
        } catch (error) {
          logger.error('❌ RabbitMQ consume error:', error);
          rabbitChannel.nack(msg, false, false); // Send to DLQ
        }
      }
    });
  } catch (error) {
    logger.error('❌ RabbitMQ consumer setup error:', error);
    throw error;
  }
};

// const publishToKafka = async (topic, message) => {
//   try {
//     await kafkaProducer.send({
//       topic,
//       messages: [
//         {
//           key: message.id || null,
//           value: JSON.stringify(message),
//           timestamp: Date.now().toString()
//         }
//       ]
//     });
//     logger.info(`📤 Published message to Kafka topic: ${topic}`);
//   } catch (error) {
//     logger.error('❌ Kafka publish error:', error);
//     throw error;
//   }
// };

// const consumeFromKafka = async (callback) => {
//   try {
//     await kafkaConsumer.run({
//       eachMessage: async ({ topic, partition, message }) => {
//         try {
//           const content = JSON.parse(message.value.toString());
//           logger.info(`📨 Received message from topic ${topic}, partition ${partition}`);
//           await callback(topic, content);
//         } catch (error) {
//           logger.error('❌ Kafka consume error:', error);
//         }
//       }
//     });
//     logger.info('🎯 Kafka consumer is now running');
//   } catch (error) {
//     logger.error('❌ Kafka consumer setup error:', error);
//     throw error;
//   }
// };

const healthCheck = async () => {
  const status = {
    rabbitmq: false,
    // kafka: {
    //   admin: false,
    //   producer: false,
    //   consumer: false
    // },
    timestamp: new Date().toISOString()
  };

  try {
    // Check RabbitMQ
    if (rabbitConnection && !rabbitConnection.connection.stream.destroyed) {
      status.rabbitmq = true;
    }

    // Check Kafka components
    // if (kafkaAdmin) {
    //   try {
    //     await kafkaAdmin.listTopics();
    //     status.kafka.admin = true;
    //   } catch (e) {
    //     logger.warn('Kafka admin health check failed:', e.message);
    //   }
    // }

    // Note: KafkaJS doesn't provide direct health check for producer/consumer
    // We assume they're healthy if admin is healthy and connections exist
    // if (kafkaProducer && status.kafka.admin) {
    //   status.kafka.producer = true;
    // }

    // if (kafkaConsumer && status.kafka.admin) {
    //   status.kafka.consumer = true;
    // }

  } catch (error) {
    logger.error('Health check error:', error);
  }

  return status;
};

const gracefulShutdown = async () => {
  logger.info('🛑 Initiating graceful shutdown...');

  try {
    // if (kafkaConsumer) {
    //   await kafkaConsumer.disconnect();
    //   logger.info('✅ Kafka consumer disconnected');
    // }

    // if (kafkaProducer) {
    //   await kafkaProducer.disconnect();
    //   logger.info('✅ Kafka producer disconnected');
    // }

    // if (kafkaAdmin) {
    //   await kafkaAdmin.disconnect();
    //   logger.info('✅ Kafka admin disconnected');
    // }

    if (rabbitConnection) {
      await rabbitConnection.close();
      logger.info('✅ RabbitMQ connection closed');
    }

  } catch (error) {
    logger.error('❌ Error during graceful shutdown:', error);
  }
};

module.exports = {
  initializeQueues,
  publishToRabbit,
  consumeFromRabbit,
  // publishToKafka,
  // consumeFromKafka,
  healthCheck,
  gracefulShutdown,
  getRabbitChannel: () => rabbitChannel,
  // getKafkaProducer: () => kafkaProducer,
  // getKafkaConsumer: () => kafkaConsumer,
  // getKafkaAdmin: () => kafkaAdmin
};