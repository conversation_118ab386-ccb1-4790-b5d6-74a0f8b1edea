const { logger } = require('../utils/logger');

const checkDatabase = async (container) => {
  try {
    const { masterDb, replicaDb } = require('../config/database.config');
    
    await masterDb.authenticate();
    await replicaDb.authenticate();
    
    return { status: 'healthy', latency: 0 };
  } catch (error) {
    logger.error('Database health check failed:', error);
    return { status: 'unhealthy', error: error.message };
  }
};

const checkCache = async (container) => {
  try {
    const { getRedisClient } = require('../config/cache.config');
    const client = getRedisClient();
    
    const start = Date.now();
    await client.ping();
    const latency = Date.now() - start;
    
    return { status: 'healthy', latency };
  } catch (error) {
    logger.error('Cache health check failed:', error);
    return { status: 'unhealthy', error: error.message };
  }
};

const checkQueue = async (container) => {
  try {
    const { getRabbitChannel } = require('../config/queue.config');
    const channel = getRabbitChannel();
    
    if (channel) {
      return { status: 'healthy' };
    }
    
    return { status: 'unhealthy', error: 'Channel not available' };
  } catch (error) {
    logger.error('Queue health check failed:', error);
    return { status: 'unhealthy', error: error.message };
  }
};

const healthRoutes = async (fastify, options) => {
  // Liveness probe - is the application running?
  fastify.get('/health/live', async (request, reply) => {
    return {
      status: 'ok',
      timestamp: new Date().toISOString()
    };
  });

  // Readiness probe - is the application ready to serve traffic?
  fastify.get('/health/ready', async (request, reply) => {
    const checks = {
      database: await checkDatabase(request.server.container),
      cache: await checkCache(request.server.container),
      queue: await checkQueue(request.server.container)
    };

    const isHealthy = Object.values(checks).every(check => check.status === 'healthy');
    const statusCode = isHealthy ? 200 : 503;

    reply.code(statusCode).send({
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      checks
    });
  });

  // Detailed health check
  fastify.get('/health', async (request, reply) => {
    const systemInfo = {
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      pid: process.pid,
      version: process.version,
      env: process.env.NODE_ENV
    };

    const checks = {
      database: await checkDatabase(request.server.container),
      cache: await checkCache(request.server.container),
      queue: await checkQueue(request.server.container)
    };

    const isHealthy = Object.values(checks).every(check => check.status === 'healthy');

    return {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      system: systemInfo,
      checks
    };
  });
};

module.exports = { healthRoutes };