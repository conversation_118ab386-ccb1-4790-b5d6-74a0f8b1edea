const { logger } = require('../../../utils/logger');

const authController = {

  async register(request, reply) {
    try {
      
      const validator = request.server.container.resolve('userValidator');
      const validatedData = await validator.validate(request.body, 'userCreate');
      const userMediator = request.server.container.resolve('userMediator');
      const result = await userMediator.createUser(validatedData);
      // const result = await userMediator.createUser(request.body);

      reply.code(201).send(result);

    } catch (error) {
      logger.error('Register user failed:', error);
      throw error;
    }
  },
  async login(request, reply) {
    try {
      
      const jwt = request.server.jwt
      const validator = request.server.container.resolve('userValidator')
      const validatedData = await validator.validate(request.body, 'login');
      const authMediator = request.server.container.resolve('authMediator');
      const result = await authMediator.login(validatedData, jwt);

      reply.send(result);

    } catch (error) {
      logger.error('Lo<PERSON> failed:', error);
      throw error;
    }
  },

  // async refreshToken(request, reply) {
  //   try {
  //     console.log('Refresh token controller:', request.body);
  //     const authMediator = request.server.container.resolve('authMediator');
  //     const result = await authMediator.refreshToken(request.body, request);

  //     reply.send(result);

  //   } catch (error) {
  //     logger.error('Token refresh failed:', error);
  //     throw error;
  //   }
  // },

  // async logout(request, reply) {
  //   try {
  //     console.log('Logout controller:', request.body);
  //     const authMediator = request.server.container.resolve('authMediator');
  //     const result = await authMediator.logout(request.user);
  //     reply.send(result);

  //   } catch (error) {
  //     logger.error('Logout failed:', error);
  //     throw error;
  //   }
  // },

  async resetPassword(request, reply) {
    try {
      console.log('Reset password controller:', request.body);
      const authMediator = request.server.container.resolve('authMediator');
      const result = await authMediator.resetPassword(request.body);
      reply.send(result);

    } catch (error) {
      logger.error('Password reset failed:', error);
      throw error;
    }
  },

  

  async requestPasswordReset(request, reply) {
    try {
      const authMediator = request.server.container.resolve('authMediator');
      const result = await authMediator.requestPasswordReset(request.body);
      reply.send(result);

    } catch (error) {
      logger.error('Forgot password failed:', error);
      throw error;
    }
  }
};

module.exports = { authController };
