const Joi = require('joi');
const { logger } = require('./logger');

const createValidator = (schemas) => {
  return {
    async validate(data, schemaName) {
      const schema = schemas[schemaName];

      if (!schema) {
        throw new Error(`Validation schema '${schemaName}' not found`);
      }

      try {
        const value = await schema.validateAsync(data, {
          abortEarly: false,
          stripUnknown: true,
          convert: true
        });
        return value;
      } catch (error) {
        console.error('validation failed ', error);
        if (error.isJoi) {

          const validationError = new Error('Validation failed');
          validationError.statusCode = 400;
          validationError.details = error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message
          }));

          logger.debug('Validation error:', {
            schema: schemaName,
            errors: validationError.details
          });

          throw validationError;
        }
        throw error;
      }
    },

    isEmail(email) {
      const schema = Joi.string().email();
      const { error } = schema.validate(email);
      return !error;
    },

    isUUID(uuid) {
      const schema = Joi.string().uuid();
      const { error } = schema.validate(uuid);
      return !error;
    },

    isPhoneNumber(phone) {
      const schema = Joi.string().pattern(/^\+?[1-9]\d{1,14}$/);
      const { error } = schema.validate(phone);
      return !error;
    },

    isURL(url) {
      const schema = Joi.string().uri();
      const { error } = schema.validate(url);
      return !error;
    },

    sanitizeInput(input) {
      if (typeof input === 'string') {
        return input
          .trim()
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/[<>]/g, '');
      }
      return input;
    },

    validatePagination(query) {
      const schema = Joi.object({
        page: Joi.number().integer().min(1).default(1),
        limit: Joi.number().integer().min(1).max(100).default(20),
        sort: Joi.string().default('createdAt'),
        order: Joi.string().valid('asc', 'desc').default('desc')
      });

      return schema.validate(query).value;
    },

    validateDateRange(startDate, endDate) {
      const schema = Joi.object({
        startDate: Joi.date().iso().required(),
        endDate: Joi.date().iso().min(Joi.ref('startDate')).required()
      });

      const { error, value } = schema.validate({ startDate, endDate });

      if (error) {
        throw new Error('Invalid date range');
      }

      return value;
    },

    customValidators: {
      password: (value) => {
        const minLength = 8;
        const hasUpperCase = /[A-Z]/.test(value);
        const hasLowerCase = /[a-z]/.test(value);
        const hasNumbers = /\d/.test(value);
        const hasSpecialChar = /[!@#$%^&*]/.test(value);

        const errors = [];

        if (value.length < minLength) {
          errors.push(`Password must be at least ${minLength} characters long`);
        }
        if (!hasUpperCase) {
          errors.push('Password must contain at least one uppercase letter');
        }
        if (!hasLowerCase) {
          errors.push('Password must contain at least one lowercase letter');
        }
        if (!hasNumbers) {
          errors.push('Password must contain at least one number');
        }
        if (!hasSpecialChar) {
          errors.push('Password must contain at least one special character');
        }

        return {
          isValid: errors.length === 0,
          errors
        };
      },

      username: (value) => {
        const minLength = 3;
        const maxLength = 30;
        const validPattern = /^[a-zA-Z0-9_-]+$/;

        const errors = [];

        if (value.length < minLength || value.length > maxLength) {
          errors.push(`Username must be between ${minLength} and ${maxLength} characters`);
        }
        if (!validPattern.test(value)) {
          errors.push('Username can only contain letters, numbers, underscores, and hyphens');
        }

        return {
          isValid: errors.length === 0,
          errors
        };
      }
    }
  };
};

module.exports = { createValidator };