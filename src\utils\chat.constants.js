// Chat related constants and utilities for Rentle app

const MESSAGE_TYPES = {
  TEXT: 'text',
  IMAGE: 'image',
  DOCUMENT: 'document',
  LOCATION: 'location',
  PRICE_NEGOTIATION: 'price_negotiation',
  QUICK_RESPONSE: 'quick_response',
  SYSTEM: 'system'
};

const MESSAGE_STATUS = {
  SENT: 'sent',
  DELIVERED: 'delivered',
  READ: 'read',
  FAILED: 'failed'
};

const USER_STATUS = {
  ONLINE: 'online',
  AWAY: 'away',
  BUSY: 'busy',
  OFFLINE: 'offline'
};

const CONVERSATION_EVENTS = {
  JOIN: 'conversation:join',
  LEAVE: 'conversation:leave',
  JOINED: 'conversation:joined',
  LEFT: 'conversation:left'
};

const MESSAGE_EVENTS = {
  SEND: 'message:send',
  SENT: 'message:sent',
  RECEIVE: 'message:receive',
  NEW: 'message:new',
  READ: 'message:read',
  DELIVERED: 'message:delivered'
};

const TYPING_EVENTS = {
  START: 'typing:start',
  STOP: 'typing:stop',
  INDICATOR: 'typing:indicator'
};

const USER_EVENTS = {
  STATUS: 'user:status',
  STATUS_CHANGED: 'user:status-changed',
  ONLINE: 'user:online',
  OFFLINE: 'user:offline'
};

const QUICK_RESPONSES = [
  "Is this still available?",
  "What's the price for a week?",
  "Can we negotiate the price?",
  "When can I pick it up?",
  "Do you provide delivery?",
  "Is insurance included?",
  "Thank you!",
  "I'm interested"
];

const PRICE_NEGOTIATION_OPTIONS = [
  { percentage: 5, label: "5% less" },
  { percentage: 10, label: "10% less" },
  { percentage: 15, label: "15% less" },
  { percentage: 20, label: "20% less" }
];

// Helper functions
const generateConversationId = (user1Id, user2Id, propertyId = null) => {
  const sortedIds = [user1Id, user2Id].sort();
  const baseId = `conv_${sortedIds[0]}_${sortedIds[1]}`;
  return propertyId ? `${baseId}_${propertyId}` : baseId;
};

const generateMessageId = () => {
  return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

const formatTimestamp = (timestamp) => {
  return new Date(timestamp).toISOString();
};

const validateMessageData = (data) => {
  const errors = [];

  if (!data.recipientId) {
    errors.push('Recipient ID is required');
  }

  if (!data.conversationId) {
    errors.push('Conversation ID is required');
  }

  if (!data.content || data.content.trim().length === 0) {
    errors.push('Message content cannot be empty');
  }

  if (data.content && data.content.length > 4000) {
    errors.push('Message content is too long (max 4000 characters)');
  }

  if (data.messageType && !Object.values(MESSAGE_TYPES).includes(data.messageType)) {
    errors.push('Invalid message type');
  }

  return {
    isValid: errors.length === 0,
    errors,
    error: errors.length > 0 ? errors[0] : null // Return first error for compatibility
  };
};

const createRoomName = (type, id) => {
  return `${type}:${id}`;
};

module.exports = {
  MESSAGE_TYPES,
  MESSAGE_STATUS,
  USER_STATUS,
  CONVERSATION_EVENTS,
  MESSAGE_EVENTS,
  TYPING_EVENTS,
  USER_EVENTS,
  QUICK_RESPONSES,
  PRICE_NEGOTIATION_OPTIONS,
  generateConversationId,
  generateMessageId,
  formatTimestamp,
  validateMessageData,
  createRoomName
};
