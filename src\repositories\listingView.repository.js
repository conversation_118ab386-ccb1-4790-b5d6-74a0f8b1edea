const { createBaseRepository } = require('./index');
const { cache } = require('../config/cache.config');
const { logger } = require('../utils/logger');
const { Op } = require('sequelize');

const createListingViewRepository = ({ models }) => {
  console.log('🔍 Creating listing view repository with models:', models);
  
  if (!models || !models.ListingView) {
    console.error('❌ ListingView model not found in models:', models);
    throw new Error('ListingView model not available');
  }

  const ListingView = models.ListingView;
  const baseRepository = createBaseRepository(ListingView);

  return {
    ...baseRepository,

    /**
     * Check if a user has already viewed a listing
     * @param {string} listingId - The listing ID
     * @param {string|null} userId - The user ID (null for anonymous)
     * @param {string|null} ipAddress - IP address for anonymous users
     * @param {string|null} userAgent - User agent for anonymous users
     * @param {string|null} sessionId - Session ID for anonymous users
     * @returns {Promise<boolean>}
     */
    async hasUserViewed(listingId, userId = null, ipAddress = null, userAgent = null, sessionId = null) {
      try {
        const { getModels } = require('../models');
        const models = getModels('read');
        
        let whereClause = { ListingID: listingId };

        if (userId) {
          // For authenticated users, check by user ID
          whereClause.UserID = userId;
        } else {
          // For anonymous users, check by session ID first, then IP + UserAgent
          if (sessionId) {
            whereClause.SessionID = sessionId;
          } else if (ipAddress && userAgent) {
            whereClause = {
              ...whereClause,
              UserID: null,
              IPAddress: ipAddress,
              UserAgent: userAgent
            };
          } else {
            return false; // Can't identify anonymous user
          }
        }

        const existingView = await models.ListingView.findOne({
          where: whereClause
        });

        return !!existingView;
      } catch (error) {
        logger.error('Error checking if user has viewed listing:', error);
        return false;
      }
    },

    /**
     * Record a new view for a listing
     * @param {Object} viewData - View data
     * @returns {Promise<Object>}
     */
    async recordView(viewData) {
      try {
        const { getModels } = require('../models');
        const models = getModels('write');
        
        const view = await models.ListingView.create({
          ListingID: viewData.listingId,
          UserID: viewData.userId || null,
          IPAddress: viewData.ipAddress || null,
          UserAgent: viewData.userAgent || null,
          SessionID: viewData.sessionId || null,
          Referrer: viewData.referrer || null,
          DeviceType: viewData.deviceType || 'unknown',
          ViewedAt: new Date()
        });

        logger.info(`View recorded for listing ${viewData.listingId}`, {
          viewId: view.ViewID,
          userId: viewData.userId,
          isAnonymous: !viewData.userId
        });

        return view;
      } catch (error) {
        logger.error('Error recording view:', error);
        throw error;
      }
    },

    /**
     * Get unique view count for a listing
     * @param {string} listingId - The listing ID
     * @returns {Promise<number>}
     */
    async getUniqueViewCount(listingId) {
      try {
        const { getModels } = require('../models');
        const models = getModels('read');
        
        const count = await models.ListingView.count({
          where: { ListingID: listingId },
          distinct: true,
          col: 'ViewID'
        });

        return count;
      } catch (error) {
        logger.error('Error getting unique view count:', error);
        return 0;
      }
    },

    /**
     * Get view statistics for a listing
     * @param {string} listingId - The listing ID
     * @param {number} days - Number of days to look back (default: 30)
     * @returns {Promise<Object>}
     */
    async getViewStatistics(listingId, days = 30) {
      try {
        const { getModels } = require('../models');
        const models = getModels('read');
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);

        const [totalViews, uniqueUsers, anonymousViews, recentViews] = await Promise.all([
          // Total views
          models.ListingView.count({
            where: { ListingID: listingId }
          }),
          
          // Unique authenticated users
          models.ListingView.count({
            where: { 
              ListingID: listingId,
              UserID: { [Op.ne]: null }
            },
            distinct: true,
            col: 'UserID'
          }),
          
          // Anonymous views
          models.ListingView.count({
            where: { 
              ListingID: listingId,
              UserID: null
            }
          }),
          
          // Recent views (last N days)
          models.ListingView.count({
            where: { 
              ListingID: listingId,
              ViewedAt: { [Op.gte]: startDate }
            }
          })
        ]);

        return {
          totalViews,
          uniqueUsers,
          anonymousViews,
          recentViews,
          period: `${days} days`
        };
      } catch (error) {
        logger.error('Error getting view statistics:', error);
        return {
          totalViews: 0,
          uniqueUsers: 0,
          anonymousViews: 0,
          recentViews: 0,
          period: `${days} days`
        };
      }
    },

    /**
     * Get daily view counts for a listing
     * @param {string} listingId - The listing ID
     * @param {number} days - Number of days to look back
     * @returns {Promise<Array>}
     */
    async getDailyViewCounts(listingId, days = 7) {
      try {
        const { getModels } = require('../models');
        const models = getModels('read');
        const { masterDb } = require('../config/database.config');
        
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);

        const dailyViews = await models.ListingView.findAll({
          attributes: [
            [masterDb.fn('DATE', masterDb.col('ViewedAt')), 'date'],
            [masterDb.fn('COUNT', masterDb.col('ViewID')), 'views']
          ],
          where: {
            ListingID: listingId,
            ViewedAt: { [Op.gte]: startDate }
          },
          group: [masterDb.fn('DATE', masterDb.col('ViewedAt'))],
          order: [[masterDb.fn('DATE', masterDb.col('ViewedAt')), 'ASC']]
        });

        return dailyViews;
      } catch (error) {
        logger.error('Error getting daily view counts:', error);
        return [];
      }
    },

    /**
     * Clean up old view records (for data retention)
     * @param {number} daysToKeep - Number of days to keep records
     * @returns {Promise<number>}
     */
    async cleanupOldViews(daysToKeep = 365) {
      try {
        const { getModels } = require('../models');
        const models = getModels('write');
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

        const deletedCount = await models.ListingView.destroy({
          where: {
            ViewedAt: { [Op.lt]: cutoffDate }
          }
        });

        logger.info(`Cleaned up ${deletedCount} old view records older than ${daysToKeep} days`);
        return deletedCount;
      } catch (error) {
        logger.error('Error cleaning up old views:', error);
        return 0;
      }
    }
  };
};

module.exports = { createListingViewRepository };
