const path = require('path');
const { logger } = require('../utils/logger');

// Environment validation
const requiredEnvVars = [
  'NODE_ENV',
  'PORT',
  'JWT_SECRET',
  'DB_MASTER_HOST',
  'DB_REPLICA_HOST'
];

const validateEnvironment = () => {
  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
};

// Application configuration
const appConfig = {
  // Basic app settings
  app: {
    name: process.env.APP_NAME || 'Enterprise Node.js App',
    version: process.env.APP_VERSION || '1.0.0',
    description: 'Enterprise-grade Node.js application with advanced architecture',
    env: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.PORT) || 3000,
    host: process.env.HOST || '0.0.0.0',
    timezone: process.env.TZ || 'UTC',
    locale: process.env.LOCALE || 'en',
    baseUrl: process.env.BASE_URL || `http://localhost:${process.env.PORT || 3000}`,
    apiVersion: process.env.API_VERSION || 'v1',
    apiPrefix: process.env.API_PREFIX || '/api/v1'
  },

  // Server configuration
  server: {
    trustProxy: process.env.TRUST_PROXY === 'true',
    bodyLimit: parseInt(process.env.BODY_LIMIT) || 10485760, // 10MB
    requestTimeout: parseInt(process.env.REQUEST_TIMEOUT) || 30000, // 30 seconds
    keepAliveTimeout: parseInt(process.env.KEEP_ALIVE_TIMEOUT) || 65000,
    headersTimeout: parseInt(process.env.HEADERS_TIMEOUT) || 66000,
    maxRequestsPerSocket: parseInt(process.env.MAX_REQUESTS_PER_SOCKET) || 0,
    requestIdHeader: process.env.REQUEST_ID_HEADER || 'x-request-id',
    requestIdLogLabel: process.env.REQUEST_ID_LOG_LABEL || 'requestId'
  },

  // Security configuration
  security: {
    jwt: {
      secret: process.env.JWT_SECRET,
      algorithm: process.env.JWT_ALGORITHM || 'HS256',
      expiresIn: process.env.JWT_EXPIRES_IN || '7d',
      issuer: process.env.JWT_ISSUER || 'enterprise-app',
      audience: process.env.JWT_AUDIENCE || 'enterprise-app-users'
    },
    refreshToken: {
      expiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN || '30d',
      length: parseInt(process.env.REFRESH_TOKEN_LENGTH) || 32
    },
    bcrypt: {
      rounds: parseInt(process.env.BCRYPT_ROUNDS) || 10
    },
    cors: {
      origin: process.env.CORS_ORIGIN || '*',
      credentials: process.env.CORS_CREDENTIALS === 'true',
      methods: (process.env.CORS_METHODS || 'GET,HEAD,PUT,PATCH,POST,DELETE').split(','),
      allowedHeaders: (process.env.CORS_ALLOWED_HEADERS || 'Content-Type,Authorization').split(','),
      exposedHeaders: (process.env.CORS_EXPOSED_HEADERS || 'x-request-id,x-response-time').split(','),
      maxAge: parseInt(process.env.CORS_MAX_AGE) || 86400
    },
    helmet: {
      contentSecurityPolicy: process.env.CSP_ENABLED === 'true',
      crossOriginEmbedderPolicy: false,
      crossOriginOpenerPolicy: false,
      crossOriginResourcePolicy: { policy: 'cross-origin' },
      dnsPrefetchControl: true,
      frameguard: { action: 'sameorigin' },
      hidePoweredBy: true,
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
      },
      ieNoOpen: true,
      noSniff: true,
      originAgentCluster: true,
      permittedCrossDomainPolicies: false,
      referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
      xssFilter: true
    },
    rateLimit: {
      global: {
        max: parseInt(process.env.RATE_LIMIT_MAX) || 100,
        timeWindow: process.env.RATE_LIMIT_WINDOW || '1 minute',
        skipSuccessfulRequests: false,
        skipFailedRequests: false
      },
      auth: {
        max: parseInt(process.env.AUTH_RATE_LIMIT_MAX) || 5,
        timeWindow: process.env.AUTH_RATE_LIMIT_WINDOW || '15 minutes',
        skipSuccessfulRequests: true
      }
    }
  },

  // Feature flags
  features: {
    swagger: process.env.SWAGGER_ENABLED !== 'true',
    metrics: process.env.METRICS_ENABLED !== 'true',
    healthChecks: process.env.HEALTH_CHECKS_ENABLED !== 'true',
    socketio: process.env.SOCKETIO_ENABLED !== 'false',
    grpc: process.env.GRPC_ENABLED !== 'false',
    mqtt: process.env.MQTT_ENABLED !== 'false',
    caching: process.env.CACHING_ENABLED !== 'false',
    queues: process.env.QUEUES_ENABLED !== 'false',
    fileUpload: process.env.FILE_UPLOAD_ENABLED === 'true',
    emailService: process.env.EMAIL_SERVICE_ENABLED !== 'false',
    smsService: process.env.SMS_SERVICE_ENABLED === 'true',
    pushNotifications: process.env.PUSH_NOTIFICATIONS_ENABLED === 'true'
  },

  // File upload configuration
  upload: {
    maxFileSize: parseInt(process.env.UPLOAD_MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
    allowedMimeTypes: (process.env.UPLOAD_ALLOWED_MIME_TYPES || 'image/jpeg,image/png,image/gif,application/pdf').split(','),
    uploadPath: process.env.UPLOAD_PATH || path.join(process.cwd(), 'uploads'),
    tempPath: process.env.TEMP_PATH || path.join(process.cwd(), 'temp'),
    publicPath: process.env.PUBLIC_PATH || '/uploads',
    enableResize: process.env.UPLOAD_ENABLE_RESIZE === 'true',
    resizeWidth: parseInt(process.env.UPLOAD_RESIZE_WIDTH) || 800,
    resizeHeight: parseInt(process.env.UPLOAD_RESIZE_HEIGHT) || 600,
    quality: parseInt(process.env.UPLOAD_QUALITY) || 80
  },

  // Email configuration
  email: {
    provider: process.env.EMAIL_PROVIDER || 'smtp', // smtp, sendgrid, mailgun, ses
    from: process.env.EMAIL_FROM || '<EMAIL>',
    fromName: process.env.EMAIL_FROM_NAME || 'Enterprise App',
    replyTo: process.env.EMAIL_REPLY_TO,
    smtp: {
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT) || 587,
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      },
      pool: process.env.SMTP_POOL === 'true',
      maxConnections: parseInt(process.env.SMTP_MAX_CONNECTIONS) || 5,
      maxMessages: parseInt(process.env.SMTP_MAX_MESSAGES) || 100
    },
    sendgrid: {
      apiKey: process.env.SENDGRID_API_KEY
    },
    mailgun: {
      apiKey: process.env.MAILGUN_API_KEY,
      domain: process.env.MAILGUN_DOMAIN
    },
    ses: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.AWS_REGION || 'us-east-1'
    },
    templates: {
      path: process.env.EMAIL_TEMPLATES_PATH || path.join(process.cwd(), 'templates', 'email'),
      engine: process.env.EMAIL_TEMPLATE_ENGINE || 'handlebars'
    }
  },

  // SMS configuration
  sms: {
    provider: process.env.SMS_PROVIDER || 'twilio', // twilio, sns, nexmo
    twilio: {
      accountSid: process.env.TWILIO_ACCOUNT_SID,
      authToken: process.env.TWILIO_AUTH_TOKEN,
      from: process.env.TWILIO_FROM_NUMBER
    },
    sns: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.AWS_REGION || 'us-east-1'
    },
    nexmo: {
      apiKey: process.env.NEXMO_API_KEY,
      apiSecret: process.env.NEXMO_API_SECRET,
      from: process.env.NEXMO_FROM_NUMBER
    }
  },

  // Push notification configuration
  push: {
    firebase: {
      projectId: process.env.FIREBASE_PROJECT_ID,
      privateKey: process.env.FIREBASE_PRIVATE_KEY,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      databaseURL: process.env.FIREBASE_DATABASE_URL
    },
    apns: {
      keyId: process.env.APNS_KEY_ID,
      teamId: process.env.APNS_TEAM_ID,
      bundleId: process.env.APNS_BUNDLE_ID,
      privateKey: process.env.APNS_PRIVATE_KEY,
      production: process.env.APNS_PRODUCTION === 'true'
    }
  },

  // Monitoring and logging
  monitoring: {
    prometheus: {
      enabled: process.env.PROMETHEUS_ENABLED !== 'false',
      port: parseInt(process.env.PROMETHEUS_PORT) || 9090,
      endpoint: process.env.PROMETHEUS_ENDPOINT || '/metrics'
    },
    sentry: {
      enabled: process.env.SENTRY_ENABLED === 'true',
      dsn: process.env.SENTRY_DSN,
      environment: process.env.NODE_ENV,
      tracesSampleRate: parseFloat(process.env.SENTRY_TRACES_SAMPLE_RATE) || 0.1
    },
    newrelic: {
      enabled: process.env.NEW_RELIC_ENABLED === 'true',
      licenseKey: process.env.NEW_RELIC_LICENSE_KEY,
      appName: process.env.NEW_RELIC_APP_NAME
    }
  },

  // Logging configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'json', // json, pretty
    destination: process.env.LOG_DESTINATION || 'stdout', // stdout, file, syslog
    file: {
      path: process.env.LOG_FILE_PATH || path.join(process.cwd(), 'logs', 'app.log'),
      maxSize: process.env.LOG_FILE_MAX_SIZE || '10m',
      maxFiles: parseInt(process.env.LOG_FILE_MAX_FILES) || 5
    },
    syslog: {
      host: process.env.SYSLOG_HOST || 'localhost',
      port: parseInt(process.env.SYSLOG_PORT) || 514,
      facility: process.env.SYSLOG_FACILITY || 'local0'
    }
  },

  // Swagger/OpenAPI configuration
  swagger: {
    title: process.env.SWAGGER_TITLE || 'Enterprise API',
    description: process.env.SWAGGER_DESCRIPTION || 'Enterprise Node.js API documentation',
    version: process.env.SWAGGER_VERSION || '1.0.0',
    host: process.env.SWAGGER_HOST,
    basePath: process.env.SWAGGER_BASE_PATH || '/api/v1',
    schemes: (process.env.SWAGGER_SCHEMES || 'http,https').split(','),
    contact: {
      name: process.env.SWAGGER_CONTACT_NAME || 'API Team',
      email: process.env.SWAGGER_CONTACT_EMAIL || '<EMAIL>',
      url: process.env.SWAGGER_CONTACT_URL || 'https://example.com'
    },
    license: {
      name: process.env.SWAGGER_LICENSE_NAME || 'MIT',
      url: process.env.SWAGGER_LICENSE_URL || 'https://opensource.org/licenses/MIT'
    },
    servers: [
      {
        url: process.env.SWAGGER_SERVER_URL || 'http://localhost:3000',
        description: process.env.SWAGGER_SERVER_DESCRIPTION || 'Development server'
      }
    ]
  },

  // External services
  external: {
    timeout: parseInt(process.env.EXTERNAL_TIMEOUT) || 10000,
    retries: parseInt(process.env.EXTERNAL_RETRIES) || 3,
    retryDelay: parseInt(process.env.EXTERNAL_RETRY_DELAY) || 1000
  },

  // Cleanup and maintenance
  cleanup: {
    tempFiles: {
      enabled: process.env.CLEANUP_TEMP_FILES !== 'false',
      maxAge: parseInt(process.env.CLEANUP_TEMP_MAX_AGE) || 24 * 60 * 60 * 1000, // 24 hours
      interval: parseInt(process.env.CLEANUP_TEMP_INTERVAL) || 60 * 60 * 1000 // 1 hour
    },
    sessions: {
      enabled: process.env.CLEANUP_SESSIONS !== 'false',
      maxAge: parseInt(process.env.CLEANUP_SESSIONS_MAX_AGE) || 7 * 24 * 60 * 60 * 1000, // 7 days
      interval: parseInt(process.env.CLEANUP_SESSIONS_INTERVAL) || 24 * 60 * 60 * 1000 // 24 hours
    },
    logs: {
      enabled: process.env.CLEANUP_LOGS !== 'false',
      maxAge: parseInt(process.env.CLEANUP_LOGS_MAX_AGE) || 30 * 24 * 60 * 60 * 1000, // 30 days
      interval: parseInt(process.env.CLEANUP_LOGS_INTERVAL) || 24 * 60 * 60 * 1000 // 24 hours
    }
  },

  // Development tools
  development: {
    hotReload: process.env.HOT_RELOAD === 'true',
    debugMode: process.env.DEBUG_MODE === 'true',
    mockData: process.env.MOCK_DATA === 'true',
    seedDatabase: process.env.SEED_DATABASE === 'true',
    enablePlayground: process.env.ENABLE_PLAYGROUND === 'true'
  }
};

// Configuration validation
const validateConfig = () => {
  try {
    validateEnvironment();
    
    // Validate critical configurations
    if (!appConfig.security.jwt.secret) {
      throw new Error('JWT_SECRET is required');
    }
    
    if (appConfig.security.jwt.secret.length < 32) {
      logger.warn('JWT_SECRET should be at least 32 characters long for security');
    }
    
    if (appConfig.app.env === 'production') {
      // Production-specific validations
      if (appConfig.security.cors.origin === '*') {
        logger.warn('CORS origin is set to "*" in production. Consider restricting it.');
      }
      
      if (!appConfig.monitoring.sentry.enabled && !appConfig.monitoring.newrelic.enabled) {
        logger.warn('No error monitoring service enabled in production');
      }
    }
    
    // Validate file upload paths
    if (appConfig.features.fileUpload) {
      const fs = require('fs');
      
      if (!fs.existsSync(appConfig.upload.uploadPath)) {
        fs.mkdirSync(appConfig.upload.uploadPath, { recursive: true });
      }
      
      if (!fs.existsSync(appConfig.upload.tempPath)) {
        fs.mkdirSync(appConfig.upload.tempPath, { recursive: true });
      }
    }
    
    logger.info('Application configuration validated successfully');
    
  } catch (error) {
    logger.error('Configuration validation failed:', error);
    throw error;
  }
};

// Initialize configuration
const initializeConfig = () => {
  try {
    validateConfig();
    
    // Set process title
    process.title = appConfig.app.name;
    
    // Set timezone
    process.env.TZ = appConfig.app.timezone;
    
    logger.info('Application configuration initialized', {
      name: appConfig.app.name,
      version: appConfig.app.version,
      env: appConfig.app.env,
      port: appConfig.app.port
    });
    
    return appConfig;
    
  } catch (error) {
    logger.error('Failed to initialize configuration:', error);
    process.exit(1);
  }
};

// Get configuration by path
const getConfig = (path) => {
  return path.split('.').reduce((obj, key) => obj?.[key], appConfig);
};

// Check if feature is enabled
const isFeatureEnabled = (feature) => {
  return getConfig(`features.${feature}`) === true;
};

// Get environment-specific config
const getEnvConfig = () => {
  const env = appConfig.app.env;
  const envConfigs = {
    development: {
      database: { logging: true },
      cache: { showFriendlyErrorStack: true },
      server: { ignoreTrailingSlash: true }
    },
    test: {
      database: { logging: false },
      cache: { showFriendlyErrorStack: false },
      logging: { level: 'silent' }
    },
    staging: {
      database: { logging: false },
      monitoring: { enabled: true }
    },
    production: {
      database: { logging: false },
      cache: { showFriendlyErrorStack: false },
      security: { trustProxy: true },
      monitoring: { enabled: true }
    }
  };
  
  return envConfigs[env] || {};
};

module.exports = {
  ...appConfig,
  initializeConfig,
  validateConfig,
  getConfig,
  isFeatureEnabled,
  getEnvConfig
};