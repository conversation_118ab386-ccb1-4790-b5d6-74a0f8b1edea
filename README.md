# Enterprise Node.js Application

A production-ready Node.js application with advanced architecture patterns, horizontal scaling support, and comprehensive infrastructure.

## 🚀 Features

- **Module Revealing Pattern** with functional programming
- **Master-Replica Database** setup with automatic read/write splitting
- **Multiple Edge Handlers**: HTTP (Fastify), gRPC, Socket.io, MQTT
- **Redis Cluster** for caching and Socket.io adapter
- **Message Queuing**: RabbitMQ and Kafka integration
- **JWT Authentication** with global hooks
- **Prometheus Monitoring** with Pino logger
- **Auto-discovery DI** system
- **PM2 Cluster Mode** with Nginx load balancing
- **Comprehensive Testing** suite

## 📋 Prerequisites

- Node.js >= 16.x
- Docker & Docker Compose
- PM2 (for production)
- Nginx (for load balancing)

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd enterprise-node-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start infrastructure services**
   ```bash
   docker-compose up -d
   ```

5. **Initialize Redis Cluster**
   ```bash
   docker exec -it redis-node-1 redis-cli --cluster create \
     127.0.0.1:7000 127.0.0.1:7001 127.0.0.1:7002 \
     --cluster-replicas 0 --cluster-yes
   ```

6. **Run database migrations**
   ```bash
   npm run db:migrate
   ```

7. **Seed the database (optional)**
   ```bash
   npm run db:seed
   ```

## 🏃‍♂️ Running the Application

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
# Using PM2
npm run pm2:start

# Or directly
NODE_ENV=production npm start
```

### With Nginx Load Balancer
1. Copy nginx configuration
   ```bash
   sudo cp infrastructure/nginx.conf /etc/nginx/sites-available/app
   sudo ln -s /etc/nginx/sites-available/app /etc/nginx/sites-enabled/
   ```

2. Restart Nginx
   ```bash
   sudo nginx -s reload
   ```

## 🧪 Testing

### Run all tests
```bash
npm test
```

### Run specific test suites
```bash
npm run test:unit        # Unit tests
npm run test:integration # Integration tests
npm run test:e2e        # End-to-end tests
```

### Test Socket.io Redis Adapter
```bash
node clients/socketio-test-client.js
```

## 📚 API Documentation

Swagger documentation is available at:
```
http://localhost:3000/documentation
```

## 🔧 Code Generation

Generate a new module:
```bash
npm run generate:module <module-name>
# Example: npm run generate:module product
```

This will create:
- Model with Sequelize definition
- Repository with caching
- Service with business logic
- Mediator for request handling
- Controller for HTTP endpoints
- Schema for validation
- Test files

## 📊 Monitoring

### Prometheus Metrics
```
http://localhost:9090/metrics
```

### Health Checks
- Liveness: `/health/live`
- Readiness: `/health/ready`

### Grafana Dashboard
```
http://localhost:3001
Username: admin
Password: admin
```

## 🏗️ Architecture

See [planning.md](planning.md) for detailed architecture documentation.

## 📁 Project Structure

```
src/
├── config/         # Configuration files
├── edge/           # Edge handlers (HTTP, gRPC, Socket.io, MQTT)
├── mediators/      # Business logic mediators
├── services/       # Business services
├── repositories/   # Data access layer
├── models/         # Database models
├── schemas/        # Validation schemas
├── strategies/     # Strategy pattern implementations
├── utils/          # Utility functions
├── hooks/          # Global hooks
├── di/             # Dependency injection
└── monitoring/     # Monitoring setup
```

## 🚀 Deployment

### Using PM2
```bash
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### Using Docker
```bash
docker build -t app .
docker run -p 3000:3000 --env-file .env app
```

## 🔒 Security

- JWT authentication with refresh tokens
- Request validation using Joi
- Rate limiting per endpoint
- CORS configuration
- Helmet for security headers
- Environment variable validation

## 📈 Performance

- Redis caching with automatic invalidation
- Database connection pooling
- Horizontal scaling with PM2 cluster
- Load balancing with Nginx
- Async message processing
- Optimized database queries

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📝 License

MIT License