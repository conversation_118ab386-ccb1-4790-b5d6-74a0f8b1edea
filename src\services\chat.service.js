const { logger } = require('../utils/logger');
const { publishToRabbit } = require('../config/queue.config');
const { v4: uuidv4 } = require('uuid');

// Message validation helper
const validateMessageData = (data) => {
  const errors = [];

  if (!data.conversationId) errors.push('conversationId is required');
  if (!data.senderId) errors.push('senderId is required');
  if (!data.recipientId) errors.push('recipientId is required');
  if (!data.content || data.content.trim().length === 0) errors.push('content is required');

  // listingId is optional - can be for any listing type (property, vehicle, furniture, etc.)

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Message status constants
const MESSAGE_STATUS = {
  SENT: 'sent',
  DELIVERED: 'delivered',
  READ: 'read'
};

// Generate unique message ID
const generateMessageId = () => `msg_${uuidv4()}`;

// Format timestamp
const formatTimestamp = (date) => date.toISOString();

const createChatService = ({ chatRepository, userRepository, listingRepository }) => {
  return {

    async createConversation(participant1Id, participant2Id, listingId = null) {
      try {
        // Validate participants exist
        const participant1 = await userRepository.findById(participant1Id);
        const participant2 = await userRepository.findById(participant2Id);

        if (!participant1 || !participant2) {
          throw new Error('One or both participants not found');
        }

        // Validate listing if provided (can be any type: property, vehicle, furniture, etc.)
        if (listingId) {
          const listing = await listingRepository.findById(listingId);
          if (!listing) {
            throw new Error('Listing not found');
          }
        }

        const conversation = await chatRepository.createOrGetConversation(
          participant1Id,
          participant2Id,
          listingId
        );

        logger.info('Conversation created/retrieved', {
          id: conversation.id,
          participant1Id,
          participant2Id,
          listingId
        }); return {
          success: true,
          conversation: {
            id: conversation.conversationId,
            participants: [
              {
                id: participant1.id,
                name: participant1.name,
                avatar: participant1.avatar,
                isOnline: false // This would come from cache/socket tracking
              },
              {
                id: participant2.id,
                name: participant2.name,
                avatar: participant2.avatar,
                isOnline: false
              }
            ],
            propertyId,
            createdAt: conversation.createdAt
          }
        };
      } catch (error) {
        logger.error('Error creating conversation:', error);
        throw error;
      }
    },

    async sendMessage(messageData) {
      try {
        // Validate message data
        const validation = validateMessageData(messageData);
        if (!validation.isValid) {
          throw new Error(validation.errors.join(', '));
        }

        // Generate message ID if not provided
        if (!messageData.id) {
          messageData.id = generateMessageId();
        }

        // Ensure conversation exists
        await chatRepository.createOrGetConversation(
          messageData.senderId,
          messageData.recipientId,
          messageData.listingId || messageData.propertyId // Support both for backward compatibility
        );

        // Save message to database
        const savedMessage = await chatRepository.saveMessage({
          ...messageData,
          status: MESSAGE_STATUS.SENT,
          timestamp: formatTimestamp(new Date())
        });

        logger.info('Message sent successfully', {
          messageId: savedMessage.messageId,
          conversationId: savedMessage.conversationId,
          senderId: savedMessage.senderId,
          recipientId: savedMessage.recipientId
        });

        return {
          success: true,
          message: {
            id: savedMessage.messageId,
            senderId: savedMessage.senderId,
            recipientId: savedMessage.recipientId,
            conversationId: savedMessage.conversationId,
            content: savedMessage.content,
            messageType: savedMessage.messageType,
            status: savedMessage.status,
            timestamp: savedMessage.createdAt,
            listingId: savedMessage.propertyId // Keep propertyId field but expose as listingId
          }
        };
      } catch (error) {
        logger.error('Error sending message:', error);
        throw error;
      }
    },

    async getUserConversations(userId, page = 1, limit = 20) {
      try {
        const conversations = await chatRepository.getUserConversations(userId, page, limit);

        // Format conversations for frontend
        const formattedConversations = conversations.map(conv => ({
          id: conv.id,
          participants: conv.participants.map(p => ({
            id: p.id,
            name: p.name,
            avatar: p.avatar,
            isOnline: false, // Would come from socket/cache tracking
            lastSeen: new Date().toISOString() // Would come from actual data
          })),
          otherParticipant: {
            id: conv.otherParticipant.id,
            name: conv.otherParticipant.name,
            avatar: conv.otherParticipant.avatar,
            isOnline: false,
            lastSeen: new Date().toISOString()
          },
          propertyInfo: conv.propertyInfo ? {
            id: conv.propertyInfo.id,
            title: conv.propertyInfo.title,
            image: conv.propertyInfo.images?.[0] || null,
            price: conv.propertyInfo.price,
            location: conv.propertyInfo.location
          } : null,
          lastMessage: conv.lastMessage,
          lastActivity: conv.lastMessageAt,
          unreadCount: conv.unreadCount,
          createdAt: conv.createdAt
        }));

        return {
          success: true,
          conversations: formattedConversations,
          pagination: {
            page,
            limit,
            total: formattedConversations.length
          }
        };
      } catch (error) {
        logger.error('Error getting user conversations:', error);
        throw error;
      }
    },

    async getConversationMessages(conversationId, userId, page = 1, limit = 50) {
      try {
        const result = await chatRepository.getConversationMessages(
          conversationId,
          userId,
          page,
          limit
        );

        // Format messages for frontend
        const formattedMessages = result.messages.map(msg => ({
          id: msg.id,
          senderId: msg.senderId,
          content: msg.content,
          timestamp: msg.timestamp,
          messageType: msg.messageType,
          status: msg.status,
          readAt: msg.readAt,
          attachmentUrl: msg.attachmentUrl,
          sender: {
            id: msg.sender.id,
            name: msg.sender.name,
            avatar: msg.sender.avatar
          }
        }));

        return {
          success: true,
          conversationId: result.conversationId,
          messages: formattedMessages,
          pagination: {
            page: result.page,
            limit: result.limit,
            total: result.totalMessages,
            hasMore: (result.page * result.limit) < result.totalMessages
          }
        };
      } catch (error) {
        logger.error('Error getting conversation messages:', error);
        throw error;
      }
    },

    async markMessageAsRead(messageId, userId) {
      try {
        const updated = await chatRepository.updateMessageStatus(
          messageId,
          MESSAGE_STATUS.READ,
          new Date()
        );

        if (!updated) {
          throw new Error('Message not found or already read');
        }

        logger.debug('Message marked as read', { messageId, userId });

        return {
          success: true,
          messageId,
          status: MESSAGE_STATUS.READ,
          readAt: new Date().toISOString()
        };
      } catch (error) {
        logger.error('Error marking message as read:', error);
        throw error;
      }
    },

    async markConversationAsRead(conversationId, userId) {
      try {
        const updatedCount = await chatRepository.markMessagesAsRead(conversationId, userId);

        logger.info('Conversation messages marked as read', {
          conversationId,
          userId,
          messagesUpdated: updatedCount
        });

        return {
          success: true,
          conversationId,
          messagesMarkedAsRead: updatedCount
        };
      } catch (error) {
        logger.error('Error marking conversation as read:', error);
        throw error;
      }
    },

    async deleteMessage(messageId, userId) {
      try {
        await chatRepository.deleteMessage(messageId, userId);

        logger.info('Message deleted', { messageId, userId });

        return {
          success: true,
          messageId,
          deletedAt: new Date().toISOString()
        };
      } catch (error) {
        logger.error('Error deleting message:', error);
        throw error;
      }
    },

    async searchMessages(userId, query, limit = 20) {
      try {
        if (!query || query.trim().length < 2) {
          throw new Error('Search query must be at least 2 characters long');
        }

        const messages = await chatRepository.searchMessages(
          userId,
          query.trim(),
          limit
        );

        const formattedMessages = messages.map(msg => ({
          id: msg.messageId,
          content: msg.content,
          conversationId: msg.conversationId,
          timestamp: msg.createdAt,
          sender: {
            id: msg.sender.id,
            name: msg.sender.name,
            avatar: msg.sender.avatar
          }
        }));

        return {
          success: true,
          query,
          messages: formattedMessages,
          total: formattedMessages.length
        };
      } catch (error) {
        logger.error('Error searching messages:', error);
        throw error;
      }
    },

    async getConversationStats(userId) {
      try {
        const conversations = await chatRepository.getUserConversations(userId, 1, 1000);

        const totalConversations = conversations.length;
        const totalUnreadMessages = conversations.reduce((sum, conv) => sum + conv.unreadCount, 0);
        const activeConversations = conversations.filter(conv =>
          conv.lastMessageAt &&
          new Date(conv.lastMessageAt) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
        ).length;

        return {
          success: true,
          stats: {
            totalConversations,
            totalUnreadMessages,
            activeConversations
          }
        };
      } catch (error) {
        logger.error('Error getting conversation stats:', error);
        throw error;
      }
    },

    // Additional methods needed by chat handler
    async getConversationById(conversationId) {
      try {
        return await chatRepository.getConversationById(conversationId);
      } catch (error) {
        logger.error('Error getting conversation by ID:', error);
        throw error;
      }
    },

    async getConversationWithMessages(conversationId, options = {}) {
      try {
        const { userId, limit = 50, offset = 0 } = options;

        const conversation = await chatRepository.getConversationById(conversationId);
        if (!conversation) {
          throw new Error('Conversation not found');
        }

        // Check if user is participant
        const isParticipant = conversation.user1Id === userId || conversation.user2Id === userId;
        if (!isParticipant) {
          throw new Error('Unauthorized access to conversation');
        }

        const page = Math.floor(offset / limit) + 1;
        const messagesResult = await chatRepository.getConversationMessages(conversationId, userId, page, limit);
        const totalMessages = await chatRepository.getConversationMessageCount(conversationId);

        return {
          conversation,
          messages: messagesResult.messages || [],
          hasMore: offset + (messagesResult.messages || []).length < totalMessages,
          total: totalMessages
        };
      } catch (error) {
        logger.error('Error getting conversation with messages:', error);
        throw error;
      }
    },

    async createMessage(messageData) {
      try {
        // Use existing sendMessage method
        const result = await this.sendMessage(messageData);
        return result.message;
      } catch (error) {
        logger.error('Error creating message:', error);
        throw error;
      }
    },

    async markMessagesAsDelivered(conversationId, userId) {
      try {
        return await chatRepository.markMessagesAsDelivered(conversationId, userId);
      } catch (error) {
        logger.error('Error marking messages as delivered:', error);
        throw error;
      }
    },

    async updateConversationActivity(conversationId) {
      try {
        return await chatRepository.updateLastActivity(conversationId);
      } catch (error) {
        logger.error('Error updating conversation activity:', error);
        throw error;
      }
    },

    async getUserConversationsWithPagination(userId, options = {}) {
      try {
        const { limit = 20, offset = 0 } = options;
        const page = Math.floor(offset / limit) + 1;

        const conversations = await chatRepository.getUserConversations(userId, page, limit);
        const totalConversations = await chatRepository.getUserConversationCount(userId);

        return {
          conversations: conversations || [],
          hasMore: offset + limit < totalConversations,
          total: totalConversations
        };
      } catch (error) {
        logger.error('Error getting user conversations with pagination:', error);
        throw error;
      }
    }
  };
};

module.exports = { createChatService };
