function withAuth(handler, jwt, container) {
    const allowList = [
        './app.UserService.CreateUser'
    ]

    return async (call, callback) => {
        const path = call.call?.handler?.path; // e.g., "/app.UserService/GetUser"

        if (allowList.includes(path)) {
            return handler(call, callback);
        }

        try {
            const auth = call.metadata.get('authorization')[0];
            const [scheme, token] = auth?.split(' ') ?? [];

            if (scheme !== 'Bearer' || !token) {
                throw new Error('Invalid or missing token');
            }

            const decoded = await jwt.verify(token);

            const userRepo = container.resolve('userRepository');
            const user = await userRepo.findById(decoded.id);

            if (!user || user.status !== 'active') {
                throw new Error('User not authorized');
            }

            // Attach user to call if needed
            call.user = user;

            return handler(call, callback);
        } catch (err) {
            return callback({
                code: grpc.status.UNAUTHENTICATED,
                message: err.message
            });
        }
    };
}

module.exports = withAuth;