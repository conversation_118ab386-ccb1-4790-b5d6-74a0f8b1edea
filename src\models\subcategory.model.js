const { DataTypes } = require('sequelize');

const defineSubcategoryModel = (sequelize) => {
  const Subcategory = sequelize.define('Subcategory', {
    SubcategoryID: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    CategoryID: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'categories',
        key: 'CategoryID'
      }
    },
    Name: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    Description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    IconURL: {
      type: DataTypes.STRING,
      allowNull: true
    },
    IsActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    CreatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    UpdatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'subcategories',
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['CategoryID', 'Name']
      }
    ],
    hooks: {
      beforeUpdate: (subcategory) => {
        subcategory.UpdatedAt = new Date();
      }
    }
  });

  Subcategory.associate = (models) => {
    Subcategory.belongsTo(models.Category, {
      foreignKey: 'CategoryID',
      as: 'Category'
    });

    Subcategory.hasMany(models.Listing, {
      foreignKey: 'SubcategoryID',
      as: 'Listings'
    });
  };

  return Subcategory;
};

module.exports = defineSubcategoryModel;
