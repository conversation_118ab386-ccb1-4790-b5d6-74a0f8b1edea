global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']  # Adjust the port if your exporter runs on another

  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['rabbitmq-exporter:15692']  # ✅ Scrape the exporter instead

  # - job_name: 'kafka-jmx'
  #   static_configs:
  #     - targets: ['kafka:7070']

  # - job_name: 'kafka-exporter'
  #   static_configs:
  #     - targets: ['kafka-exporter:9308']

  - job_name: 'mqtt'
    static_configs:
      - targets: ['mqtt-exporter:9641']  # Assuming you're using mqtt-exporter on this port

  # - job_name: 'zookeeper'
  #   static_configs:
  #     - targets: ['zookeeper-exporter:9141']

