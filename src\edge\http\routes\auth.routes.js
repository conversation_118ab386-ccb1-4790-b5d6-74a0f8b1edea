const { authController } = require('../controllers/auth.controller');
const { optionalAuth } = require('../../../hooks/auth.hook');

const authRoutes = async (fastify, options) => {
  // Register user
  fastify.post('/register', {
    schema: {
      description: 'Register a new user',
      tags: ['auth'],
      body: {
        type: 'object',
        required: ['email', 'password', 'fullName'],
        properties: {
          email: { type: 'string', format: 'email' },
          // username: { type: 'string' },
          password: { type: 'string', minLength: 8 },
          fullName: { type: 'string' }

        }
      },
      response: {
        201: {
          description: 'User registered successfully',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                // username: { type: 'string' },
                fullName: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, authController.register);

  // Login
  fastify.post('/login', {
    schema: {
      description: 'User login',
      tags: ['auth'],
      body: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string' }
        }
      }
    }
  }, authController.login);

  // Refresh token
  // fastify.post('/refresh', {
  //   schema: {
  //     description: 'Refresh access token',
  //     tags: ['auth'],
  //     body: {
  //       type: 'object',
  //       required: ['refreshToken'],
  //       properties: {
  //         refreshToken: { type: 'string' }
  //       }
  //     }
  //   }
  // }, authController.refreshToken);

  // Logout
  // fastify.post('/logout', {
  //   preHandler: [optionalAuth],
  //   schema: {
  //     description: 'User logout',
  //     tags: ['auth']
  //   }
  // }, authController.logout);

  // Forgot password
  // fastify.post('/forgot-password', {
  //   schema: {
  //     description: 'Request password reset',
  //     tags: ['auth'],
  //     body: {
  //       type: 'object',
  //       required: ['email'],
  //       properties: {
  //         email: { type: 'string', format: 'email' }
  //       }
  //     }
  //   }
  // }, authController.requestPasswordReset);

  // Reset password
  // fastify.post('/reset-password', {
  //   schema: {
  //     description: 'Reset password with token',
  //     tags: ['auth'],
  //     body: {
  //       type: 'object',
  //       required: ['token', 'newPassword'],
  //       properties: {
  //         token: { type: 'string' },
  //         newPassword: { type: 'string', minLength: 8 }
  //       }
  //     }
  //   }
  // }, authController.resetPassword);
};

module.exports = authRoutes;