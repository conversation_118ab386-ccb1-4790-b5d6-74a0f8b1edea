const cloudinary = require('cloudinary').v2;
const { logger } = require('../utils/logger');

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
  secure: true
});

// Test Cloudinary connection
const testCloudinaryConnection = async () => {
  try {
    const result = await cloudinary.api.ping();
    logger.info('✅ Cloudinary connection successful:', result);
    return true;
  } catch (error) {
    logger.error('❌ Cloudinary connection failed:', error.message);
    return false;
  }
};

/**
 * Upload image to Cloudinary
 * @param {Buffer} fileBuffer - Image file buffer
 * @param {string} folder - Cloudinary folder name
 * @param {string} publicId - Custom public ID (optional)
 * @returns {Promise<Object>} Cloudinary upload result
 */
const uploadImage = async (fileBuffer, folder = 'rental-listings', publicId = null) => {
  try {
    logger.info('🔄 Starting Cloudinary upload...');

    const options = {
      folder: folder,
      resource_type: 'image',
      quality: 'auto:good',
      width: 1200,
      height: 800,
      crop: 'limit',
      unique_filename: true,
      use_filename: true
    };

    if (publicId) {
      options.public_id = publicId;
    }

    const result = await new Promise((resolve, reject) => {
      const uploadStream = cloudinary.uploader.upload_stream(
        options,
        (error, result) => {
          if (error) {
            logger.error('❌ Cloudinary upload error:', error);
            reject(new Error(`Cloudinary upload failed: ${error.message}`));
          } else {
            logger.info('✅ Cloudinary upload successful:', result.public_id);
            resolve(result);
          }
        }
      );

      uploadStream.end(fileBuffer);
    });

    return {
      url: result.secure_url,
      publicId: result.public_id,
      width: result.width,
      height: result.height,
      format: result.format,
      bytes: result.bytes
    };
  } catch (error) {
    logger.error('❌ Error uploading image to Cloudinary:', error);
    throw error;
  }
};

/**
 * Delete image from Cloudinary
 * @param {string} publicId - Cloudinary public ID
 * @returns {Promise<Object>} Deletion result
 */
const deleteImage = async (publicId) => {
  try {
    const result = await cloudinary.uploader.destroy(publicId);
    logger.info('Image deleted from Cloudinary:', publicId);
    return result;
  } catch (error) {
    logger.error('Error deleting image from Cloudinary:', error);
    throw new Error('Failed to delete image');
  }
};

/**
 * Upload multiple images to Cloudinary
 * @param {Array<Buffer>} fileBuffers - Array of image file buffers
 * @param {string} folder - Cloudinary folder name
 * @returns {Promise<Array>} Array of upload results
 */
const uploadMultipleImages = async (fileBuffers, folder = 'rental-listings') => {
  try {
    const uploadPromises = fileBuffers.map((buffer, index) => 
      uploadImage(buffer, folder, `${Date.now()}_${index}`)
    );
    
    const results = await Promise.all(uploadPromises);
    logger.info(`Uploaded ${results.length} images to Cloudinary`);
    return results;
  } catch (error) {
    logger.error('Error uploading multiple images:', error);
    throw new Error('Failed to upload images');
  }
};

module.exports = {
  cloudinary,
  uploadImage,
  deleteImage,
  uploadMultipleImages,
  testCloudinaryConnection
};

