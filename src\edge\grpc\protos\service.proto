syntax = "proto3";

package app;

// User messages
message User {
  string id = 1;
  string email = 2;
  string username = 3;
  string firstName = 4;
  string lastName = 5;
  string role = 6;
  string status = 7;
  string createdAt = 8;
}

message GetUserRequest {
  string userId = 1;
}

message GetUserResponse {
  bool success = 1;
  User data = 2;
}

message CreateUserRequest {
  string email = 1;
  string username = 2;
  string password = 3;
  string firstName = 4;
  string lastName = 5;
}

message CreateUserResponse {
  message Data{
    string id = 1;
    string email = 2;
    string username = 3;
  }
  bool success = 1;
  Data data = 2;
}

message UserAddress {
  string street = 1;
  string city = 2;
  string state = 3;
  string country = 4;
  string postalCode = 5;
}

message UpdateUserRequest {
  string firstName = 1;
  string lastName = 2;
  string phone = 3;
  string dateOfBirth = 4;
  UserAddress address = 5;
}

message UpdateUserResponse{
  bool success = 1;
  AuthUser user = 2;
}

message DeleteUserRequest{
  string userId = 1;
}

message DeleteUserResponse{
  bool success = 1;
  string message = 2;
}

message ListUsersRequest {
  int32 page = 1;
  int32 limit = 2;
  string status = 3;
  string role = 4;
}

message ListUsersResponse {
  repeated User users = 1;
  int32 total = 2;
  int32 page = 3;
  int32 total_pages = 4;
}

// Order messages
message OrderItem {
  string productId = 1;
  string name = 2;
  string price = 3;
  int32 quantity = 4;
}

message Address {
  string fullName = 1;
  string street = 2;
  string city = 3;
  string state = 4;
  string country = 5;
  string postalCode = 6;
  string phone = 7;
}

message Order {
  string id = 1;
  string orderNumber = 2;
  string userId = 3;
  string status = 4;
  string total = 5;
  string currency = 6;
  string createdAt = 7;
  string updatedAt = 8;
}

message GetOrderRequest {
  string orderId = 1;
}

message GetOrderResponse {
  Order order = 1;
}

message CreateOrderRequest {
  string userId = 1;
  repeated OrderItem items = 2;
  Address shippingAddress = 3;
  string paymentMethod = 4;
}

message CreateOrderResponse {
  Order order = 1;
}

message StreamOrdersRequest {
  string userId = 1;
  string status = 2;
}

message StreamOrdersResponse {
  Order order = 1;
}

message OrderUpdateRequest {
  string orderId = 1;
  string status = 2;
}

message OrderUpdateResponse {
  Order order = 1;
}

message CancleOrderRequest{
  string userId = 1;
  string orderId = 2;
  string reason = 3;
}

message CancleOrderResposne{
  bool success = 1;
  Order data = 2;
  string message = 3;  
}

message AuthUser {
  message UserPref{

  }
  message UserMetaData{

  }

  string id = 1;
  string email = 2;
  string username = 3;
  string firstName = 4;
  string lastName = 5;
  string role = 6;
  string status = 7;
  bool emailVerified = 8;
  string lastLoginAt = 9;
  int32 loginAttempts = 10;
  string lockedUntil = 11;
  UserPref preferences = 12;
  UserMetaData metadata = 13;
  string createdAt = 14;
  string updatedAt = 15;
}

message AuthData {
  AuthUser user = 1;
  string accessToken = 2;
  string refreshToken = 3;
  int64 expiresIn = 4;
}

message AuthResponse {
  bool success = 1;
  AuthData data = 2;
}

message LoginRequest{
  string email = 1;
  string password = 2;
}

// Services
service UserService {
  rpc GetUser (GetUserRequest) returns (GetUserResponse);
  rpc CreateUser (CreateUserRequest) returns (CreateUserResponse);
  rpc DeleteUser (DeleteUserRequest) returns (DeleteUserResponse);
  rpc ListUsers (ListUsersRequest) returns (ListUsersResponse);
}

service OrderService {
  rpc GetOrder (GetOrderRequest) returns (GetOrderResponse);
  rpc CreateOrder (CreateOrderRequest) returns (CreateOrderResponse);
  rpc CancelOrder (CancleOrderRequest) returns (CancleOrderResposne);
  rpc StreamOrders (StreamOrdersRequest) returns (stream StreamOrdersResponse);
  rpc OrderUpdates (stream OrderUpdateRequest) returns (stream OrderUpdateResponse);
}

service AuthService{
  rpc Register (CreateUserRequest) returns (CreateUserResponse);
  rpc Login (LoginRequest) returns (AuthResponse);
}