const pino = require('pino');
const path = require('path');

// Create logger instance
const logger = pino({
  name: 'enterprise-app',
  level: process.env.LOG_LEVEL || 'debug',
  timestamp: pino.stdTimeFunctions.isoTime,
  formatters: {
    level: (label) => {
      return { level: label };
    }
  },
  serializers: {
    req: (req) => ({
      id: req.id,
      method: req.method,
      url: req.url,
      query: req.query,
      params: req.params,
      headers: {
        'user-agent': req.headers['user-agent'],
        'content-type': req.headers['content-type']
      },
      remoteAddress: req.ip,
      remotePort: req.socket.remotePort
    }),
    res: (res) => ({
      statusCode: res.statusCode,
      headers: res.getHeaders()
    }),
    err: pino.stdSerializers.err
  },
  redact: {
    paths: [
      'req.headers.authorization',
      'req.headers.cookie',
      '*.password',
      '*.token',
      '*.secret',
      '*.creditCard',
      '*.ssn'
    ],
    censor: '[REDACTED]'
  },
  transport: process.env.NODE_ENV === 'development' ? {
    target: 'pino-pretty',
    options: {
      colorize: true,
      translateTime: 'HH:MM:ss Z',
      ignore: 'pid,hostname'
    }
  } : undefined
});

// Create child loggers for different modules
const createLogger = (module) => {
  return logger.child({ module });
};

// Log levels
const logLevels = {
  fatal: 60,
  error: 50,
  warn: 40,
  info: 30,
  debug: 20,
  trace: 10
};

// Structured logging helpers
const logHelpers = {
  // Log API request
  logRequest: (req, res, responseTime) => {
    logger.info({
      type: 'api_request',
      req,
      res,
      responseTime,
      id: req.user?.id
    }, 'API Request');
  },

  // Log database query
  logQuery: (query, duration, operation) => {
    logger.debug({
      type: 'db_query',
      query,
      duration,
      operation
    }, 'Database Query');
  },

  // Log cache operation
  logCache: (operation, key, hit = null) => {
    logger.debug({
      type: 'cache_operation',
      operation,
      key,
      hit
    }, 'Cache Operation');
  },

  // Log queue message
  logQueue: (queue, operation, message) => {
    logger.debug({
      type: 'queue_message',
      queue,
      operation,
      messageId: message.id,
      messageType: message.type
    }, 'Queue Message');
  },

  // Log external API call
  logExternalApi: (service, method, url, statusCode, duration) => {
    logger.info({
      type: 'external_api',
      service,
      method,
      url,
      statusCode,
      duration
    }, 'External API Call');
  },

  // Log business event
  logBusinessEvent: (event, data) => {
    logger.info({
      type: 'business_event',
      event,
      ...data
    }, `Business Event: ${event}`);
  },

  // Log security event
  logSecurityEvent: (event, data) => {
    logger.warn({
      type: 'security_event',
      event,
      ...data
    }, `Security Event: ${event}`);
  },

  // Log performance metric
  logPerformance: (metric, value, unit = 'ms') => {
    logger.info({
      type: 'performance_metric',
      metric,
      value,
      unit
    }, 'Performance Metric');
  }
};

// Error logging with context
const logError = (error, context = {}) => {
  const errorInfo = {
    type: 'application_error',
    error: {
      message: error.message,
      stack: error.stack,
      code: error.code,
      name: error.name
    },
    ...context
  };

  if (error.statusCode >= 500 || !error.statusCode) {
    logger.error(errorInfo, 'Application Error');
  } else {
    logger.warn(errorInfo, 'Client Error');
  }
};

// Audit logging
const auditLog = (action, id, resourceType, resourceId, changes = {}) => {
  logger.info({
    type: 'audit_log',
    action,
    id,
    resourceType,
    resourceId,
    changes,
    timestamp: new Date().toISOString()
  }, `Audit: ${action} on ${resourceType}`);
};

module.exports = {
  logger,
  createLogger,
  logLevels,
  ...logHelpers,
  logError,
  auditLog
};