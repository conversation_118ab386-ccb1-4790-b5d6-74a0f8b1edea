const { logger } = require('../../utils/logger');
const { metrics } = require('../../monitoring/prometheus');
const jwt = require('jsonwebtoken');

const eventSchemas = {
  'order:create': 'orderCreate',
  'order:update': 'orderUpdate',
  'order:getAll': 'orderQuery',

  'user:update': 'userUpdate',

  'login': 'login',
  'register': 'userCreate',

  // Chat event schemas
  'chat:join': 'chatJoin',
  'chat:leave': 'chatLeave',
  'message:send': 'messageSend',
  'message:mark-read': 'messageMarkRead',
  'conversation:get': 'conversationGet',
  'conversations:list': 'conversationsList',
  'typing:start': 'typingStart',
  'typing:stop': 'typingStop'
}

const authMiddleware = (io, container) => {
  return async (socket, next) => {
    try {
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization;
      if (!token) {
        return next(new Error('Authentication required'));
      }

      // Remove 'Bearer ' prefix if present
      const cleanToken = token.replace('Bearer ', '');

      try {
        // Verify JWT token
        if (cleanToken == process.env.SPECIAL_SOCKET_TOKEN) {
          socket.userId = null;
          socket.userEmail = null;
          socket.userRole = null;
          return next();
        }

        const decoded = jwt.verify(cleanToken, process.env.JWT_SECRET);

        // Get user from database (optional for now)
        try {
          const userRepository = container.resolve('userRepository');
          const user = await userRepository.findById(decoded.id);

          if (user && !user.isDeleted) {
            // Use database user info
            socket.userId = user.id;
            socket.userEmail = user.email;
            socket.userRole = user.role || 'user';
          } else {
            // Use token data if user not found or deleted
            socket.userId = decoded.id;
            socket.userEmail = decoded.email;
            socket.userRole = 'user';
          }
        } catch (dbError) {
          // If database check fails, still allow connection with token data
          socket.userId = decoded.id;
          socket.userEmail = decoded.email;
          socket.userRole = 'user';
        }

        logger.debug('Socket authenticated', {
          socketId: socket.id,
          userId: socket.userId
        });

        next();
      } catch (error) {
        logger.error(`Socket auth error:, ${error}`);
        next(new Error('Invalid token'));
      }
    } catch (error) {
      logger.error(`Authentication failed ${error.message}`)
      next(new Error('Authentication failed'));
    }
  };
};

function defineSocketPerformanceMiddleware(container) {

  return function (socket, next) {

    // Store original emit and on methods
    const originalOn = socket.on;

    // Override on method to measure performance
    socket.on = function (event, handler) {
      originalOn.call(socket, event, async (...args) => {

        try {
          if (['disconnect'].includes(event)) return await handler;

          if (typeof args !== 'object') throw Error('Unexpected data in socket event')
          const parsedData = JSON.parse(args);

          let validator = parsedData;
          if (event.includes("order")) {
            validator = container.resolve("orderValidator");
          }

          if (['user', 'login', 'register'].some(keyword => event.includes(keyword))) {
            validator = container.resolve("userValidator");
          }

          // Chat events validation
          if (['chat', 'message', 'conversation'].some(keyword => event.includes(keyword))) {
            validator = container.resolve("chatValidator");
          }

          // // Validate UUID
          if ((parsedData.userId !== undefined) && (!validator.isUUID(parsedData.userId))) {
            throw new Error('Invalid user ID format');
          }

          let validatedData = parsedData;
          const schemaKey = eventSchemas[event];

          if (schemaKey) {
            validatedData = await validator.validate(parsedData, schemaKey);
          }

          const start = performance.now();
          await handler(...args);
          const duration = performance.now() - start;

          metrics.recordSocketEvent(event, duration);

          // Log performance metrics
          console.log(`[Performance] Event received: ${event}, Time: ${duration.toFixed(3)}ms`);
        } catch (error) {
          logger.error(`Failed to entertain the event ${event}: ${error}`)
          socket.emit('error', `${error}`)
        }
      });
    };

    next();
  }
}

const rateLimitMiddleware = (maxRequests = 100, windowMs = 60000) => {
  const requests = new Map();

  return (socket, next) => {
    const now = Date.now();
    const userId = socket.userId || socket.id;

    if (!requests.has(userId)) {
      requests.set(userId, []);
    }

    const userRequests = requests.get(userId);
    const recentRequests = userRequests.filter(timestamp => now - timestamp < windowMs);

    if (recentRequests.length >= maxRequests) {
      return next(new Error('Rate limit exceeded'));
    }

    recentRequests.push(now);
    requests.set(userId, recentRequests);

    next();
  };
};

const validationMiddleware = (schema) => {
  return (socket, next) => {
    const { error } = schema.validate(socket.data);

    if (error) {
      return next(new Error(`Validation error: ${error.message}`));
    }

    next();
  };
};

module.exports = {
  authMiddleware,
  rateLimitMiddleware,
  validationMiddleware,
  defineSocketPerformanceMiddleware
};