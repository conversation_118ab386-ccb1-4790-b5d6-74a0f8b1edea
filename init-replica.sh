#!/bin/bash
until pg_isready -h postgres-primary -p 5432 -U user; do
  echo "Waiting for primary to be ready..."
  sleep 2
done
pg_basebackup -h postgres-primary -D /var/lib/postgresql/data -U replicator -P --wal-method=stream
echo "host replication replicator postgres-primary/32 trust" >> /var/lib/postgresql/data/pg_hba.conf
echo "primary_conninfo = 'host=postgres-primary port=5432 user=replicator password=1234'" >> /var/lib/postgresql/data/postgresql.conf
echo "primary_slot_name = 'replica_slot'" >> /var/lib/postgresql/data/postgresql.conf