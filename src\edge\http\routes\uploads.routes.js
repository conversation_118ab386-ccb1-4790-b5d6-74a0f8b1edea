const UploadController = require('../controllers/upload.controller');

async function uploadsRoutes(fastify, options) {
  // Register multipart support for file uploads
  await fastify.register(require('@fastify/multipart'));

  const uploadController = new UploadController();

  // Separate image upload endpoint
  fastify.post('/images', {
    attachValidation: true, // Let handler run even if schema validation fails (multipart streams)
    schema: {
      tags: ['uploads'],
      summary: 'Upload images',
      description: 'Upload one or more images to Cloudinary and get URLs',
      consumes: ['multipart/form-data'],
      security: [{ bearerAuth: [] }],
     body: {
      type: 'object',
      required: ['files'],
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary'
          }
        }
      }
    },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  url: { type: 'string' },
                  publicId: { type: 'string' }
                }
              }
            }
          }
        },
        400: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        500: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            error: { type: 'string' }
          }
        }
      }
    }
  }, uploadController.uploadImages.bind(uploadController));
}

module.exports = uploadsRoutes;

