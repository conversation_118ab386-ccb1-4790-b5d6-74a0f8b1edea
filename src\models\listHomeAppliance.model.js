const { DataTypes } = require('sequelize');

const defineHomeApplianceModel = (sequelize) => {
  const HomeAppliance = sequelize.define('HomeAppliance', {
    ApplianceID: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    ListingID: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'listings',
        key: 'ListingID'
      }
    },
    ApplianceType: { type: DataTypes.STRING, allowNull: false },
    BrandName: { type: DataTypes.STRING, allowNull: false },
    ModelNumber: { type: DataTypes.STRING, allowNull: true },
    PowerRating: { type: DataTypes.STRING, allowNull: true }, // can store Watts or Tons
    // Condition: { type: DataTypes.ENUM('new', 'used'), allowNull: false },
    WarrantyAvailable: { type: DataTypes.BOOLEAN, allowNull: false, defaultValue: false }
  }, {
    tableName: 'home_appliances',
    timestamps: false
  });

  HomeAppliance.associate = (models) => {
    HomeAppliance.belongsTo(models.Listing, {
      foreignKey: 'ListingID',
      as: 'Listing'
    });
  };

  return HomeAppliance;
};

module.exports = defineHomeApplianceModel;
