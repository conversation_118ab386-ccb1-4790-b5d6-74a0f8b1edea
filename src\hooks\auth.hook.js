const { logger } = require('../utils/logger');
const { HTTP_STATUS, ERROR_MESSAGES } = require('../utils/constants');

const authHook = async (request, reply) => {
  try {
    // Skip auth for public routes
    const publicRoutes = [
      '/health',
      '/metrics',
      '/documentation',
      '/api/v1/auth/login',
      '/api/v1/auth/register',
      '/api/v1/auth/refresh',
      '/api/v1/auth/forgot-password',
      '/api/v1/auth/reset-password',
   // '/api/v1/users/profile'
      // Listing public routes - only GET requests should be public
      '/api/v1/listings/search',
      '/api/v1/listings/featured',
      '/api/v1/listings/categories',
      '/api/v1/listings/home'
    ];

    // Check for exact matches and pattern matches
    const isPublicRoute = publicRoutes.some(route =>
      request.url.startsWith(route)
    ) ||
    // Allow GET requests to individual listings and related endpoints
    (request.method === 'GET' && (
      request.url === '/api/v1/listings' || // GET /api/v1/listings (list all)
      request.url.match(/^\/api\/v1\/listings\/[a-f0-9-]{36}$/) || // GET /api/v1/listings/:id
      request.url.match(/^\/api\/v1\/listings\/[a-f0-9-]{36}\/related$/) || // GET /api/v1/listings/:id/related
      request.url.match(/^\/api\/v1\/listings\/categories\/[a-f0-9-]{36}$/) || // GET /api/v1/listings/categories/:id
      request.url.match(/^\/api\/v1\/listings\/categories\/[a-f0-9-]{36}\/listings/) || // GET /api/v1/listings/categories/:id/listings
      request.url.match(/^\/api\/v1\/listings\/categories\/[a-f0-9-]{36}\/subcategories/) || // GET /api/v1/listings/categories/:id/subcategories
      request.url.match(/^\/api\/v1\/listings\/categories\/[a-f0-9-]{36}\/page/) || // GET /api/v1/listings/categories/:id/page
      request.url.match(/^\/api\/v1\/listings\/subcategories\/[a-f0-9-]{36}\/listings/) // GET /api/v1/listings/subcategories/:id/listings
    ));

    if (isPublicRoute) {
      return;
    }

    // Check for authorization header
    const authHeader = request.headers.authorization;

    if (!authHeader) {
      return reply.code(HTTP_STATUS.UNAUTHORIZED).send({
        error: ERROR_MESSAGES.UNAUTHORIZED,
        message: 'Missing authorization header'
      });
    }

    // Extract token
    const [bearer, token] = authHeader.split(' ');

    if (bearer !== 'Bearer' || !token) {
      return reply.code(HTTP_STATUS.UNAUTHORIZED).send({
        error: ERROR_MESSAGES.UNAUTHORIZED,
        message: 'Invalid authorization format'
      });
    }

    try {
      // Verify token
      const decoded = await request.server.jwt.verify(token);
      // const decoded = await request.jwtVerify();

      // Check if user still exists and is active
      const userRepository = request.server.container.resolve('userRepository');
      // console.log("userRepository", userRepository)
      const user = await userRepository.findById(decoded.id);

      console.log(" auth.hook decoded status:", decoded)


      if (!user) {
        console.log(" auth.hook user:", user)
        return reply.code(HTTP_STATUS.UNAUTHORIZED).send({
          error: ERROR_MESSAGES.UNAUTHORIZED,
          message: 'User not found'
        });
      }

      // if (user.status !== 'active') {
      //   console.log(" auth.hook active user status:", user.status)
      //   return reply.code(HTTP_STATUS.FORBIDDEN).send({
      //     error: ERROR_MESSAGES.FORBIDDEN,
      //     message: 'Account is not active'
      //   });
      // }

      // Attach user to request
      request.user = {
        id: user.id,
        email: user.email,
        role: user.role,
        status: user.status
      };

      // Log successful authentication
      logger.debug('User authenticated', {
        id: user.id,
        role: user.role,
        path: request.url
      });

    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        return reply.code(HTTP_STATUS.UNAUTHORIZED).send({
          error: ERROR_MESSAGES.INVALID_TOKEN,
          message: 'Token has expired'
        }); 
      }

      if (error.name === 'JsonWebTokenError') {
        return reply.code(HTTP_STATUS.UNAUTHORIZED).send({
          error: ERROR_MESSAGES.INVALID_TOKEN,
          message: 'Invalid token'
        });
      }

      throw error;
    }

  } catch (error) {
    logger.error(`Auth hook error:, ${error}`);
    console.log('Failed to authenticate', error)
    return reply.code(HTTP_STATUS.INTERNAL_SERVER_ERROR).send({
      error: ERROR_MESSAGES.INTERNAL_ERROR,
      message: 'Authentication error'
    });
  }
};

// Role-based access control decorator
const requireRole = (...allowedRoles) => {
  return async (request, reply) => {
    if (!request.user) {
      return reply.code(HTTP_STATUS.UNAUTHORIZED).send({
        error: ERROR_MESSAGES.UNAUTHORIZED,
        message: 'Authentication required'
      });
    }

    if (!allowedRoles.includes(request.user.role)) {
      logger.warn('Access denied', {
        id: request.user.id,
        userRole: request.user.role,
        requiredRoles: allowedRoles,
        path: request.url
      });

      return reply.code(HTTP_STATUS.FORBIDDEN).send({
        error: ERROR_MESSAGES.FORBIDDEN,
        message: 'Insufficient permissions'
      });
    }
  };
};

// Optional auth - doesn't fail if no token, but attaches user if valid
const optionalAuth = async (request, reply) => {
  const authHeader = request.headers.authorization;

  if (!authHeader) {
    return;
  }

  const [bearer, token] = authHeader.split(' ');

  if (bearer === 'Bearer' && token) {
    try {
      const decoded = await request.server.jwt.verify(token);
      // const decoded = await request.jwtVerify();
      const userRepository = request.server.container.resolve('userRepository');
      const user = await userRepository.findById(decoded.id);
      console.log('BearerDecoded user:', decoded);

      if (user && user.status === 'active') {
        request.user = {
          id: user.id,
          email: user.email,
          role: user.role,
          status: user.status
        };
      }
    } catch (error) {
      // Ignore token errors for optional auth
      logger.debug('Optional auth token error:', error.message);
    }
  }
};

module.exports = { authHook, requireRole, optionalAuth };