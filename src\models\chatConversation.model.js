const { DataTypes } = require('sequelize');

const defineChatConversationModel = (sequelize) => {
  const ChatConversation = sequelize.define('ChatConversation', {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false
    },
    user1Id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    user2Id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    // Property/Listing information related to conversation
    listingId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'listings',
        key: 'ListingID'
      }
    },
    listingTitle: {
      type: DataTypes.STRING,
      allowNull: true
    },
    listingImage: {
      type: DataTypes.STRING,
      allowNull: true
    },
    listingPrice: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true
    },
    listingPriceUnit: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'Day'
    },
    listingDateRange: {
      type: DataTypes.STRING,
      allowNull: true
    },
    listingLocation: {
      type: DataTypes.STRING,
      allowNull: true
    },
    listingOwnerId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    lastActivity: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    unreadCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'chat_conversations',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['user1Id', 'user2Id', 'propertyId']
      },
      {
        fields: ['user1Id']
      },
      {
        fields: ['user2Id']
      },
      {
        fields: ['propertyId']
      },
      {
        fields: ['lastActivity']
      },
      {
        fields: ['isActive']
      }
    ]
  });

  ChatConversation.associate = (models) => {
    // User associations
    ChatConversation.belongsTo(models.User, {
      foreignKey: 'user1Id',
      as: 'user1'
    });

    ChatConversation.belongsTo(models.User, {
      foreignKey: 'user2Id',
      as: 'user2'
    });

    ChatConversation.belongsTo(models.User, {
      foreignKey: 'propertyOwnerId',
      as: 'propertyOwner'
    });

    // Listing association (if available)
    if (models.Listing) {
      ChatConversation.belongsTo(models.Listing, {
        foreignKey: 'propertyId',
        targetKey: 'ListingID',
        as: 'property'
      });
    }

    // Messages association
    ChatConversation.hasMany(models.ChatMessage, {
      foreignKey: 'conversationId',
      sourceKey: 'id',
      as: 'messages'
    });
  };

  return ChatConversation;
};

module.exports = defineChatConversationModel;
