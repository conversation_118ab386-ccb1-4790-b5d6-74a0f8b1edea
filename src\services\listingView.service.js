const { logger } = require('../utils/logger');
const { cache } = require('../config/cache.config');

const createListingViewService = ({ listingViewRepository, listingRepository }) => {
  return {
    /**
     * Track a view for a listing with unique user detection
     * @param {string} listingId - The listing ID
     * @param {Object} requestData - Request data containing user info
     * @returns {Promise<Object>}
     */
    async trackView(listingId, requestData) {
      try {
        const {
          userId = null,
          ipAddress = null,
          userAgent = null,
          sessionId = null,
          referrer = null,
          deviceType = 'unknown'
        } = requestData;

        // Check if listing exists
        const listing = await listingRepository.findById(listingId);
        if (!listing) {
          throw new Error('Listing not found');
        }

        // Check if user has already viewed this listing
        const hasViewed = await listingViewRepository.hasUserViewed(
          listingId, 
          userId, 
          ipAddress, 
          userAgent, 
          sessionId
        );

        if (hasViewed) {
          logger.debug(`User has already viewed listing ${listingId}`, {
            userId,
            ipAddress: ipAddress ? ipAddress.substring(0, 8) + '...' : null
          });
          
          return {
            success: true,
            alreadyViewed: true,
            message: 'View already recorded for this user'
          };
        }

        // Record the new view
        const viewRecord = await listingViewRepository.recordView({
          listingId,
          userId,
          ipAddress,
          userAgent,
          sessionId,
          referrer,
          deviceType
        });

        // Increment the listing's view count
        await listingRepository.incrementViewCount(listingId);

        // Clear cache for listing statistics
        await this.clearViewCache(listingId);

        logger.info(`New view tracked for listing ${listingId}`, {
          viewId: viewRecord.ViewID,
          userId,
          isAuthenticated: !!userId
        });

        return {
          success: true,
          alreadyViewed: false,
          viewId: viewRecord.ViewID,
          message: 'View recorded successfully'
        };

      } catch (error) {
        logger.error('Error tracking view:', error);
        throw error;
      }
    },

    /**
     * Get view statistics for a listing
     * @param {string} listingId - The listing ID
     * @param {number} days - Number of days for recent views
     * @returns {Promise<Object>}
     */
    async getViewStatistics(listingId, days = 30) {
      try {
        const cacheKey = `listing_stats:${listingId}:${days}d`;
        
        // Try to get from cache first
        const cached = await cache.get(cacheKey);
        if (cached) {
          return cached;
        }

        const stats = await listingViewRepository.getViewStatistics(listingId, days);
        
        // Cache for 1 hour
        await cache.set(cacheKey, stats, 3600);
        
        return stats;
      } catch (error) {
        logger.error('Error getting view statistics:', error);
        throw error;
      }
    },

    /**
     * Get daily view counts for analytics
     * @param {string} listingId - The listing ID
     * @param {number} days - Number of days to look back
     * @returns {Promise<Array>}
     */
    async getDailyViewCounts(listingId, days = 7) {
      try {
        const cacheKey = `daily_views:${listingId}:${days}d`;
        
        const cached = await cache.get(cacheKey);
        if (cached) {
          return cached;
        }

        const dailyViews = await listingViewRepository.getDailyViewCounts(listingId, days);
        
        // Cache for 30 minutes
        await cache.set(cacheKey, dailyViews, 1800);
        
        return dailyViews;
      } catch (error) {
        logger.error('Error getting daily view counts:', error);
        throw error;
      }
    },

    /**
     * Get unique view count for a listing
     * @param {string} listingId - The listing ID
     * @returns {Promise<number>}
     */
    async getUniqueViewCount(listingId) {
      try {
        const cacheKey = `unique_views:${listingId}`;
        
        const cached = await cache.get(cacheKey);
        if (cached !== null) {
          return cached;
        }

        const count = await listingViewRepository.getUniqueViewCount(listingId);
        
        // Cache for 10 minutes
        await cache.set(cacheKey, count, 600);
        
        return count;
      } catch (error) {
        logger.error('Error getting unique view count:', error);
        return 0;
      }
    },

    /**
     * Extract device type from user agent
     * @param {string} userAgent - User agent string
     * @returns {string}
     */
    detectDeviceType(userAgent) {
      if (!userAgent) return 'unknown';
      
      const ua = userAgent.toLowerCase();
      
      if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
        return 'mobile';
      } else if (ua.includes('tablet') || ua.includes('ipad')) {
        return 'tablet';
      } else {
        return 'desktop';
      }
    },

    /**
     * Generate session ID for anonymous users
     * @param {string} ipAddress - IP address
     * @param {string} userAgent - User agent
     * @returns {string}
     */
    generateSessionId(ipAddress, userAgent) {
      const crypto = require('crypto');
      const data = `${ipAddress}-${userAgent}-${Date.now()}`;
      return crypto.createHash('sha256').update(data).digest('hex').substring(0, 32);
    },

    /**
     * Clear view-related cache for a listing
     * @param {string} listingId - The listing ID
     */
    async clearViewCache(listingId) {
      try {
        const patterns = [
          `listing_stats:${listingId}:*`,
          `daily_views:${listingId}:*`,
          `unique_views:${listingId}`
        ];

        for (const pattern of patterns) {
          await cache.del(pattern);
        }
      } catch (error) {
        logger.error('Error clearing view cache:', error);
      }
    },

    /**
     * Batch process view tracking (for high-traffic scenarios)
     * @param {Array} viewRequests - Array of view tracking requests
     * @returns {Promise<Array>}
     */
    async batchTrackViews(viewRequests) {
      try {
        const results = [];
        
        for (const request of viewRequests) {
          try {
            const result = await this.trackView(request.listingId, request.requestData);
            results.push({ ...result, listingId: request.listingId });
          } catch (error) {
            logger.error(`Error in batch view tracking for listing ${request.listingId}:`, error);
            results.push({
              success: false,
              listingId: request.listingId,
              error: error.message
            });
          }
        }

        return results;
      } catch (error) {
        logger.error('Error in batch view tracking:', error);
        throw error;
      }
    },

    /**
     * Clean up old view records
     * @param {number} daysToKeep - Number of days to keep records
     * @returns {Promise<number>}
     */
    async cleanupOldViews(daysToKeep = 365) {
      try {
        const deletedCount = await listingViewRepository.cleanupOldViews(daysToKeep);
        logger.info(`Cleaned up ${deletedCount} old view records`);
        return deletedCount;
      } catch (error) {
        logger.error('Error cleaning up old views:', error);
        throw error;
      }
    }
  };
};

module.exports = { createListingViewService };
