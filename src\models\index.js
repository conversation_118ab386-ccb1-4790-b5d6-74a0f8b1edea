// src/models/index.js
const { getDatabase } = require('../config/database.config');
const defineUserModel = require('./users.model');
const defineCategoryModel = require('./category.model');
const defineSubcategoryModel = require('./subcategory.model');
const defineListingModel = require('./listing.model');
const defineFavoriteModel = require('./favorites.model');
const definePropertyModel = require('./listProperty.model');
const defineVehicleModel = require('./listVehicle.model');
const defineFurnitureModel = require('./listFurniture.model');
const defineHomeApplianceModel = require('./listHomeAppliance.model');
const definePhotographyEquipmentModel = require('./listPhotographyEquipment.model');
const defineListingViewModel = require('./listingView.model');
const defineChatConversationModel = require('./chatConversation.model');
const defineChatMessageModel = require('./chatMessage.model');

const { logger } = require('../utils/logger');

let models = {};

// Initialize models across master and replica DBs
const initializeModels = async () => {
  console.log('🚀 Initializing models...');

  const masterDb = getDatabase('write');
  const replicaDb = getDatabase('read');

  console.log('✅ Retrieved database connections (master & replica)');

  // Define models for write (master)
  console.log('📦 Defining write models...');
  models.User = defineUserModel(masterDb);
  models.Category = defineCategoryModel(masterDb);
  models.Subcategory = defineSubcategoryModel(masterDb);
  models.Listing = defineListingModel(masterDb);
  models.Favorite = defineFavoriteModel(masterDb);
  models.Property = definePropertyModel(masterDb);
  models.Vehicle = defineVehicleModel(masterDb);
  models.Furniture = defineFurnitureModel(masterDb);
  models.HomeAppliance = defineHomeApplianceModel(masterDb);
  models.PhotographyEquipment = definePhotographyEquipmentModel(masterDb);
  models.ListingView = defineListingViewModel(masterDb);
  models.ChatConversation = defineChatConversationModel(masterDb);
  models.ChatMessage = defineChatMessageModel(masterDb);


  // Define models for read (replica)
  console.log('📦 Defining read models...');
  models.UserRead = defineUserModel(replicaDb);
  models.CategoryRead = defineCategoryModel(replicaDb);
  models.SubcategoryRead = defineSubcategoryModel(replicaDb);
  models.ListingRead = defineListingModel(replicaDb);
  models.FavoriteRead = defineFavoriteModel(replicaDb);
  models.PropertyRead = definePropertyModel(replicaDb);
  models.VehicleRead = defineVehicleModel(replicaDb);
  models.FurnitureRead = defineFurnitureModel(replicaDb);
  models.HomeApplianceRead = defineHomeApplianceModel(replicaDb);
  models.PhotographyEquipmentRead = definePhotographyEquipmentModel(replicaDb);
  models.ListingViewRead = defineListingViewModel(replicaDb);
  models.ChatConversationRead = defineChatConversationModel(replicaDb);
  models.ChatMessageRead = defineChatMessageModel(replicaDb);

  // Setup associations (write)
  console.log('🔗 Setting up write associations...');
  Object.keys(models).forEach(modelName => {
    if (models[modelName].associate && !modelName.endsWith('Read')) {
      console.log(`↪️ Associating (write): ${modelName}`);
      logger.debug(`Associating write model: ${modelName}`);
      models[modelName].associate(models);
    }
  });

  // Associate read models
  console.log('🔗 Setting up read associations...');
  const readModels = {
    User: models.UserRead,
    Category: models.CategoryRead,
    Subcategory: models.SubcategoryRead,
    Listing: models.ListingRead,
    Favorite: models.FavoriteRead,
    Property: models.PropertyRead,
    Vehicle: models.VehicleRead,
    Furniture: models.FurnitureRead,
    HomeAppliance: models.HomeApplianceRead,
    PhotographyEquipment: models.PhotographyEquipmentRead,
    ListingView: models.ListingViewRead,
    ChatConversation: models.ChatConversationRead,
    ChatMessage: models.ChatMessageRead
  };

  Object.keys(readModels).forEach(modelName => {
    if (readModels[modelName].associate) {
      console.log(`↪️ Associating (read): ${modelName}Read`);
      logger.debug(`Associating read model: ${modelName}Read`);
      readModels[modelName].associate(readModels);
    }
  });

  // Sync all models in development
  if (process.env.NODE_ENV === 'development') {
    logger.info('🔁 Syncing models (development only)...');
    console.log('🔁 Starting model sync in development mode...');

    for (const [modelName, model] of Object.entries(models)) {
      try {
        console.log(`⏳ Syncing model: ${modelName}`);
        logger.debug(`🔄 Starting sync for: ${modelName}`);

        await model.sync({ alter: true });

        console.log(`✅ Synced model: ${modelName}`);
        logger.debug(`✅ Successfully synced: ${modelName}`);
      } catch (error) {
        console.error(`❌ Error syncing model: ${modelName}`, error);
        logger.error(`❌ Failed to sync ${modelName}: ${error.message}`, { stack: error.stack });
      }
    }

    console.log('✅ All models synced successfully.');
    logger.info('✅ Model sync complete');
  }

  logger.info(`🗃️ Models initialized: ${Object.keys(models).length}`);
  console.log(`🗃️ Total models initialized: ${Object.keys(models).length}`);

  return models;
};

// 🔄 Access models based on read/write operation
const getModels = (operation = 'read') => {
  const writeOps = ['create', 'update', 'delete', 'upsert', 'bulkCreate', 'bulkUpdate', 'bulkDelete', 'write'];
  const isWrite = writeOps.includes(operation);

  console.log(`🔍 getModels called with operation: ${operation} | isWrite: ${isWrite}`);

  return isWrite
    ? {
      User: models.User,
      Category: models.Category,
      Subcategory: models.Subcategory,
      Listing: models.Listing,
      Favorite: models.Favorite,
      Property: models.Property,
      Vehicle: models.Vehicle,
      Furniture: models.Furniture,
      HomeAppliance: models.HomeAppliance,
      PhotographyEquipment: models.PhotographyEquipment,
      ListingView: models.ListingView,
      ChatConversation: models.ChatConversation,
      ChatMessage: models.ChatMessage
    }
    : {
      User: models.UserRead,
      Category: models.CategoryRead,
      Subcategory: models.SubcategoryRead,
      Listing: models.ListingRead,
        Favorite: models.FavoriteRead,
      Property: models.PropertyRead,
      Vehicle: models.VehicleRead,
      Furniture: models.FurnitureRead,
      HomeAppliance: models.HomeApplianceRead,
      PhotographyEquipment: models.PhotographyEquipmentRead,
      ListingView: models.ListingViewRead,
      ChatConversation: models.ChatConversationRead,
      ChatMessage: models.ChatMessageRead
    };
};

/**
 * Get a single model by name
 * @param {string} modelName
 */
const getModel = (modelName) => {
  console.log(`🔍 getModel called with modelName: ${modelName}`);
  if (!models[modelName]) {
    console.error(`❌ Model '${modelName}' not found`);
    throw new Error(`Model '${modelName}' not found`);
  }
  return models[modelName];
};

// 🧹 Optional: Clear model cache for testing/hot reload
const clearModelsCache = () => {
  console.log('🧹 Clearing model cache...');
  models = {};
  logger.debug('🧹 Cleared model cache');
};

// 🧾 Optional: Model stats
const getModelStats = () => {
  return {
    total: Object.keys(models).length,
    names: Object.keys(models)
  };
};

// ✅ Sync all models with DB (write DB only)
const syncModels = async () => {
  try {
    const masterDb = getDatabase('write');
    await masterDb.sync({ alter: true });
    console.log('✅ Master DB models synchronized');
  } catch (err) {
    console.error('❌ Model sync failed:', err);
    throw err;
  }
};

module.exports = {
  initializeModels,
  getModels,
  getModel,
  clearModelsCache,
  getModelStats,
  syncModels, 
  models
};

