const { DataTypes } = require('sequelize');

const defineFavoriteModel = (sequelize) => {
  const Favorite = sequelize.define('Favorite', {
    ID: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false
    },
    UserID: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    },
    ListingID: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'listings',
        key: 'ListingID'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    },
    CreatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      allowNull: false
    },
    UpdatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      allowNull: false
    }
  }, {
    tableName: 'favorites',
    timestamps: true,
    createdAt: 'CreatedAt',
    updatedAt: 'UpdatedAt',
    indexes: [
      {
        unique: true,
        fields: ['UserID', 'ListingID'],
        name: 'unique_user_listing_favorite'
      },
      {
        fields: ['UserID'],
        name: 'idx_favorites_user_id'
      },
      {
        fields: ['ListingID'],
        name: 'idx_favorites_listing_id'
      }
    ]
  });

  // Define associations
  Favorite.associate = (models) => {
    Favorite.belongsTo(models.User, {
      foreignKey: 'UserID',
      as: 'User'
    });
    
    Favorite.belongsTo(models.Listing, {
      foreignKey: 'ListingID',
      as: 'Listing'
    });
  };
  

  return Favorite;
};

module.exports = defineFavoriteModel;
