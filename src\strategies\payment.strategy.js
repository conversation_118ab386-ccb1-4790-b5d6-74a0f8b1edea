const { logger } = require('../utils/logger');

// Base payment processor
const createPaymentProcessor = (type) => ({
  async process(paymentData) {
    logger.info(`Processing ${type} payment`);
    // Simulate payment processing
    return {
      success: true,
      transactionId: `${type}_${Date.now()}`,
      timestamp: new Date()
    };
  },

  async refund(transactionId, amount) {
    logger.info(`Processing ${type} refund for transaction ${transactionId}`);
    // Simulate refund processing
    return {
      success: true,
      refundId: `refund_${Date.now()}`,
      amount,
      timestamp: new Date()
    };
  }
});

// Payment processors
const paymentProcessors = {
  'credit_card': {
    async process(paymentData) {
      const { cardNumber, cardholderName, expiryMonth, expiryYear, cvv, amount } = paymentData;
      
      // Validate card details
      if (!cardNumber || !cardholderName || !expiryMonth || !expiryYear || !cvv) {
        throw new Error('Missing required card details');
      }

      // Simulate credit card processing
      logger.info('Processing credit card payment');
      
      // In production, this would integrate with payment gateway
      return {
        success: true,
        transactionId: `cc_${Date.now()}`,
        last4: cardNumber.slice(-4),
        brand: detectCardBrand(cardNumber),
        timestamp: new Date()
      };
    },

    async refund(transactionId, amount) {
      logger.info(`Processing credit card refund for ${transactionId}`);
      return {
        success: true,
        refundId: `cc_refund_${Date.now()}`,
        amount,
        timestamp: new Date()
      };
    }
  },

  'debit_card': createPaymentProcessor('debit_card'),

  'paypal': {
    async process(paymentData) {
      const { paypalEmail, amount } = paymentData;
      
      if (!paypalEmail) {
        throw new Error('PayPal email is required');
      }

      logger.info(`Processing PayPal payment for ${paypalEmail}`);
      
      // In production, integrate with PayPal API
      return {
        success: true,
        transactionId: `paypal_${Date.now()}`,
        paypalEmail,
        timestamp: new Date()
      };
    },

    async refund(transactionId, amount) {
      logger.info(`Processing PayPal refund for ${transactionId}`);
      return {
        success: true,
        refundId: `paypal_refund_${Date.now()}`,
        amount,
        timestamp: new Date()
      };
    }
  },

  'stripe': {
    async process(paymentData) {
      const { stripeToken, amount } = paymentData;
      
      if (!stripeToken) {
        throw new Error('Stripe token is required');
      }

      logger.info('Processing Stripe payment');
      
      // In production, use Stripe SDK
      return {
        success: true,
        transactionId: `stripe_${Date.now()}`,
        stripeToken,
        timestamp: new Date()
      };
    },

    async refund(transactionId, amount) {
      logger.info(`Processing Stripe refund for ${transactionId}`);
      return {
        success: true,
        refundId: `stripe_refund_${Date.now()}`,
        amount,
        timestamp: new Date()
      };
    }
  },

  'cash': {
    async process(paymentData) {
      logger.info('Processing cash payment');
      return {
        success: true,
        transactionId: `cash_${Date.now()}`,
        requiresConfirmation: true,
        timestamp: new Date()
      };
    },

    async refund(transactionId, amount) {
      logger.info(`Processing cash refund for ${transactionId}`);
      return {
        success: true,
        refundId: `cash_refund_${Date.now()}`,
        amount,
        method: 'in_person',
        timestamp: new Date()
      };
    }
  }
};

// Helper function to detect card brand
const detectCardBrand = (cardNumber) => {
  const patterns = {
    visa: /^4/,
    mastercard: /^5[1-5]/,
    amex: /^3[47]/,
    discover: /^6(?:011|5)/
  };

  for (const [brand, pattern] of Object.entries(patterns)) {
    if (pattern.test(cardNumber)) {
      return brand;
    }
  }

  return 'unknown';
};

// Strategy implementation
const createPaymentStrategy = () => {
  return {
    async execute(paymentMethod, data) {
      const processor = paymentProcessors[paymentMethod];
      
      if (!processor) {
        throw new Error(`Unsupported payment method: ${paymentMethod}`);
      }

      try {
        // Add common payment data
        const paymentData = {
          ...data.paymentData,
          amount: data.order.total,
          orderId: data.order.id,
          currency: data.order.currency || 'USD'
        };

        // Process payment
        const result = await processor.process(paymentData);

        // Log successful payment
        logger.info(`Payment successful for order ${data.order.id}`, {
          method: paymentMethod,
          transactionId: result.transactionId
        });

        return result;

      } catch (error) {
        logger.error(`Payment failed for order ${data.order.id}:`, error);
        
        return {
          success: false,
          error: error.message,
          timestamp: new Date()
        };
      }
    },

    async refund(paymentMethod, data) {
      const refundMethod = paymentMethod.replace('-refund', '');
      const processor = paymentProcessors[refundMethod];
      
      if (!processor || !processor.refund) {
        throw new Error(`Refund not supported for payment method: ${refundMethod}`);
      }

      try {
        const result = await processor.refund(
          data.order.metadata.paymentId,
          data.order.total
        );

        logger.info(`Refund successful for order ${data.order.id}`, {
          refundId: result.refundId
        });

        return result;

      } catch (error) {
        logger.error(`Refund failed for order ${data.order.id}:`, error);
        
        return {
          success: false,
          error: error.message,
          timestamp: new Date()
        };
      }
    },

    // Get available payment methods
    getAvailableMethods() {
      return Object.keys(paymentProcessors);
    },

    // Validate payment method
    isValidMethod(method) {
      return method in paymentProcessors;
    }
  };
};

module.exports = { createPaymentStrategy };