// const { Kafka } = require('kafkajs');

const testKafkaConnection = async () => {
  console.log('🚀 Starting Kafka Connection Test...\n');
  
  // Use localhost for external connection, kafka for internal Docker network
  const brokers = (process.env.KAFKA_BROKERS || 'localhost:9092').split(',').map(b => b.trim());
  console.log(`📡 Brokers: ${brokers.join(', ')}`);

  const kafka = new Kafka({
    clientId: 'test-client',
    brokers,
    connectionTimeout: 10000,
    requestTimeout: 30000,
    logLevel: 4, // INFO level
  });

  let admin = null;
  let producer = null;
  let consumer = null;

  try {
    // Test 1: Admin Connection
    console.log('\n1️⃣ Testing Admin Connection...');
    admin = kafka.admin();
    await admin.connect();
    console.log('✅ Admin connected successfully');

    // Test 2: List Existing Topics
    console.log('\n2️⃣ Listing existing topics...');
    const existingTopics = await admin.listTopics();
    console.log(`📋 Found ${existingTopics.length} topics:`);
    existingTopics.forEach(topic => console.log(`   - ${topic}`));

    // Test 3: Create Test Topics
    console.log('\n3️⃣ Creating test topics...');
    const testTopics = ['user-events', 'order-events', 'system-events'];
    const missingTopics = testTopics.filter(topic => !existingTopics.includes(topic));
    
    if (missingTopics.length > 0) {
      console.log(`🆕 Creating missing topics: ${missingTopics.join(', ')}`);
      const createResult = await admin.createTopics({
        topics: missingTopics.map(topic => ({
          topic,
          numPartitions: 3,
          replicationFactor: 1,
        })),
        waitForLeaders: true,
        timeout: 30000
      });
      
      if (createResult) {
        console.log('✅ Topics created successfully');
      }
    } else {
      console.log('✅ All test topics already exist');
    }

    // Test 4: Get Topic Metadata
    console.log('\n4️⃣ Getting topic metadata...');
    for (const topic of testTopics) {
      try {
        const metadata = await admin.fetchTopicMetadata({ topics: [topic] });
        console.log(`📊 Topic: ${topic}`);
        metadata.topics[0].partitions.forEach(partition => {
          console.log(`   - Partition ${partition.partitionId}: Leader ${partition.leader}`);
        });
      } catch (error) {
        console.log(`❌ Failed to get metadata for ${topic}: ${error.message}`);
      }
    }

    // Test 5: Producer Connection
    console.log('\n5️⃣ Testing Producer Connection...');
    producer = kafka.producer({
      allowAutoTopicCreation: false,
      transactionTimeout: 30000,
    });
    await producer.connect();
    console.log('✅ Producer connected successfully');

    // Test 6: Send Test Message
    console.log('\n6️⃣ Sending test message...');
    const testMessage = {
      id: Date.now().toString(),
      message: 'Test message from debug script',
      timestamp: new Date().toISOString()
    };

    await producer.send({
      topic: 'system-events',
      messages: [{
        key: testMessage.id,
        value: JSON.stringify(testMessage),
      }]
    });
    console.log('✅ Test message sent successfully');

    // Test 7: Consumer Connection
    console.log('\n7️⃣ Testing Consumer Connection...');
    consumer = kafka.consumer({
      groupId: 'test-group',
      sessionTimeout: 30000,
      heartbeatInterval: 3000,
    });
    await consumer.connect();
    console.log('✅ Consumer connected successfully');

    // Test 8: Subscribe to Topics
    console.log('\n8️⃣ Testing Topic Subscription...');
    for (const topic of testTopics) {
      try {
        await consumer.subscribe({ topic, fromBeginning: false });
        console.log(`✅ Subscribed to ${topic}`);
      } catch (error) {
        console.log(`❌ Failed to subscribe to ${topic}: ${error.message}`);
      }
    }

    // Test 9: Brief Consumer Test
    console.log('\n9️⃣ Testing message consumption (5 seconds)...');
    let messageReceived = false;

    const timeout = setTimeout(() => {
      console.log('⏰ Consumer test timeout - no messages received (this is normal for a fresh setup)');
    }, 5000);

    await consumer.run({
      eachMessage: async ({ topic, partition, message }) => {
        console.log(`📨 Received message from ${topic}:${partition}`);
        console.log(`   Key: ${message.key?.toString()}`);
        console.log(`   Value: ${message.value?.toString()}`);
        messageReceived = true;
        clearTimeout(timeout);
      }
    });

    // Wait for messages or timeout
    await new Promise(resolve => setTimeout(resolve, 5000));

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('✅ Admin connection: OK');
    console.log('✅ Topic listing: OK');
    console.log('✅ Topic creation: OK');
    console.log('✅ Producer connection: OK');
    console.log('✅ Message sending: OK');
    console.log('✅ Consumer connection: OK');
    console.log('✅ Topic subscription: OK');
    console.log(`${messageReceived ? '✅' : '⚠️'} Message consumption: ${messageReceived ? 'OK' : 'No messages (normal for fresh setup)'}`);

  } catch (error) {
    console.error('\n❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    // Cleanup
    console.log('\n🧹 Cleaning up connections...');
    try {
      if (consumer) {
        await consumer.disconnect();
        console.log('✅ Consumer disconnected');
      }
      if (producer) {
        await producer.disconnect();
        console.log('✅ Producer disconnected');
      }
      if (admin) {
        await admin.disconnect();
        console.log('✅ Admin disconnected');
      }
    } catch (cleanupError) {
      console.error('❌ Cleanup error:', cleanupError);
    }
  }
};

// Run the test if this script is executed directly
if (require.main === module) {
  testKafkaConnection()
    .then(() => {
      console.log('\n✨ Test completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testKafkaConnection };