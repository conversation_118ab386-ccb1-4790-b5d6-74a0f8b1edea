import paho.mqtt.client as mqtt
import time

MQTT_BROKER = "mosquitto"
MQTT_PORT = 1883
METRICS_TOPIC = "metrics/mosquitto"

def on_connect(client, userdata, flags, rc):
    print("Connected to MQTT broker")
    client.subscribe("$SYS/#")

def on_message(client, userdata, msg):
    metric_name = msg.topic.replace("$SYS/broker/", "").replace("/", "_")
    client.publish(f"{METRICS_TOPIC}/{metric_name}", msg.payload)

client = mqtt.Client()
client.on_connect = on_connect
client.on_message = on_message
client.connect(MQTT_BROKER, MQTT_PORT, 60)
client.loop_forever()