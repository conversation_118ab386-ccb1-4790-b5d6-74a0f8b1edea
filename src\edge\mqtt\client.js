const mqtt = require('mqtt');
const { logger } = require('../../utils/logger');
const { createMqttHandlers } = require('./handlers');

const startMqttClient = async (container) => {
  const brokerUrl = process.env.MQTT_BROKER_URL || 'mqtt://localhost:1883';
  const clientId = process.env.MQTT_CLIENT_ID || `app-${process.pid}`;

  const options = {
    clientId,
    clean: true,
    connectTimeout: 4000,
    username: process.env.MQTT_USERNAME,
    password: process.env.MQTT_PASSWORD,
    reconnectPeriod: 1000,
    will: {
      topic: 'app/status',
      payload: JSON.stringify({
        clientId,
        status: 'offline',
        timestamp: new Date()
      }),
      qos: 1,
      retain: true
    }
  };

  const client = mqtt.connect(brokerUrl, options);
  const handlers = createMqttHandlers(container);

  // Connection event handlers
  client.on('connect', () => {
    logger.info('✅ MQTT client connected', { clientId, broker: brokerUrl });

    // Publish online status
    client.publish('app/status', JSON.stringify({
      clientId,
      status: 'online',
      timestamp: new Date()
    }), { qos: 1, retain: true });

    // Subscribe to topics
    const topics = [
      'devices/+/telemetry',
      'devices/+/status',
      'devices/+/commands',
      'orders/+/updates',
      'alerts/+',
      'system/health',
      '$SYS/broker/clients/connected'
    ];

    topics.forEach(topic => {
      client.subscribe(topic, { qos: 1 }, (err) => {
        if (err) {
          logger.error(`Failed to subscribe to ${topic}:`, err);
        } else {
          logger.debug(`Subscribed to MQTT topic: ${topic}`);
        }
      });
    });
  });

  client.on('message', async (topic, message) => {
    try {
      const payload = message.toString();
      logger.debug(`MQTT message received on ${topic}`, { 
        size: payload.length 
      });

      // Route messages based on topic
      if (topic.startsWith('devices/')) {
        await handlers.handleDeviceMessage(topic, payload);
      } else if (topic.startsWith('orders/')) {
        await handlers.handleOrderMessage(topic, payload);
      } else if (topic.startsWith('alerts/')) {
        await handlers.handleAlertMessage(topic, payload);
      } else if (topic === 'system/health') {
        await handlers.handleHealthMessage(topic, payload);
      } else if (topic.startsWith('$SYS/')) {
        await handlers.handleSystemMessage(topic, payload);
      }
    } catch (error) {
      logger.error('MQTT message handling error:', error, {
        topic,
        error: error.message
      });
    }
  });

  client.on('error', (error) => {
    logger.error('MQTT client error:', error);
  });

  client.on('offline', () => {
    logger.warn('MQTT client is offline');
  });

  client.on('reconnect', () => {
    logger.info('MQTT client reconnecting...');
  });

  client.on('close', () => {
    logger.info('MQTT client connection closed');
  });

  // Publish helper functions
  const mqttPublisher = {
    async publishDeviceCommand(deviceId, command) {
      const topic = `devices/${deviceId}/commands`;
      const payload = JSON.stringify({
        command,
        timestamp: new Date(),
        source: clientId
      });

      return new Promise((resolve, reject) => {
        client.publish(topic, payload, { qos: 1 }, (err) => {
          if (err) {
            logger.error(`Failed to publish device command:`, err);
            reject(err);
          } else {
            logger.debug(`Device command published to ${topic}`);
            resolve();
          }
        });
      });
    },

    async publishOrderUpdate(orderId, update) {
      const topic = `orders/${orderId}/updates`;
      const payload = JSON.stringify({
        ...update,
        timestamp: new Date(),
        source: clientId
      });

      return new Promise((resolve, reject) => {
        client.publish(topic, payload, { qos: 2 }, (err) => {
          if (err) {
            logger.error(`Failed to publish order update:`, err);
            reject(err);
          } else {
            logger.debug(`Order update published to ${topic}`);
            resolve();
          }
        });
      });
    },

    async publishAlert(level, alert) {
      const topic = `alerts/${level}`;
      const payload = JSON.stringify({
        ...alert,
        timestamp: new Date(),
        source: clientId
      });

      return new Promise((resolve, reject) => {
        client.publish(topic, payload, { qos: 1, retain: true }, (err) => {
          if (err) {
            logger.error(`Failed to publish alert:`, err);
            reject(err);
          } else {
            logger.debug(`Alert published to ${topic}`);
            resolve();
          }
        });
      });
    },

    async publishMetrics(metrics) {
      const topic = 'system/metrics';
      const payload = JSON.stringify({
        ...metrics,
        timestamp: new Date(),
        clientId
      });

      return new Promise((resolve, reject) => {
        client.publish(topic, payload, { qos: 0 }, (err) => {
          if (err) {
            logger.error(`Failed to publish metrics:`, err);
            reject(err);
          } else {
            resolve();
          }
        });
      });
    }
  };

  // Attach publisher to container
  container.register('mqttPublisher', mqttPublisher);

  // Periodic health check
  setInterval(() => {
    if (client.connected) {
      const health = {
        clientId,
        status: 'healthy',
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        timestamp: new Date()
      };

      client.publish('system/health', JSON.stringify(health), { qos: 0 });
    }
  }, 30000); // Every 30 seconds

  return client;
};

module.exports = { startMqttClient };