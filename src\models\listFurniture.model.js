const { DataTypes } = require('sequelize');

const defineFurnitureModel = (sequelize) => {
  const Furniture = sequelize.define('Furniture', {
    FurnitureID: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    ListingID: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'listings',
        key: 'ListingID'
      }
    },
    FurnitureType: { type: DataTypes.STRING, allowNull: false },
    Material: { type: DataTypes.STRING, allowNull: true },
    Size: { type: DataTypes.STRING, allowNull: true },
    Color: { type: DataTypes.STRING, allowNull: true },
    // Condition: { type: DataTypes.ENUM('new', 'likeNew', 'used'), allowNull: false },
    Quantity: { type: DataTypes.INTEGER, allowNull: false, defaultValue: 1 }
  }, {
    tableName: 'furniture',
    timestamps: false
  });

  Furniture.associate = (models) => {
    Furniture.belongsTo(models.Listing, {
      foreignKey: 'ListingID',
      as: 'Listing'
    });
  };

  return Furniture;
};

module.exports = defineFurnitureModel;
