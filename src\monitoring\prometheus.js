const promClient = require('prom-client');
const { logger } = require('../utils/logger');

// Create a Registry
const register = new promClient.Registry();

// Add default metrics
promClient.collectDefaultMetrics({ register });

// Custom metrics
const httpRequestDuration = new promClient.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.5, 1, 2, 5]
});

const httpRequestTotal = new promClient.Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code']
});

const activeConnections = new promClient.Gauge({
  name: 'active_connections',
  help: 'Number of active connections'
});

const dbQueryDuration = new promClient.Histogram({
  name: 'db_query_duration_seconds',
  help: 'Duration of database queries in seconds',
  labelNames: ['operation', 'table'],
  buckets: [0.01, 0.05, 0.1, 0.5, 1]
});

const cacheHits = new promClient.Counter({
  name: 'cache_hits_total',
  help: 'Total number of cache hits',
  labelNames: ['operation']
});

const cacheMisses = new promClient.Counter({
  name: 'cache_misses_total',
  help: 'Total number of cache misses',
  labelNames: ['operation']
});

const queueSize = new promClient.Gauge({
  name: 'queue_size',
  help: 'Current size of message queues',
  labelNames: ['queue']
});

const businessMetrics = {
  ordersCreated: new promClient.Counter({
    name: 'orders_created_total',
    help: 'Total number of orders created'
  }),
  
  orderValue: new promClient.Histogram({
    name: 'order_value_dollars',
    help: 'Order value in dollars',
    buckets: [10, 50, 100, 500, 1000, 5000]
  }),
  
  userRegistrations: new promClient.Counter({
    name: 'user_registrations_total',
    help: 'Total number of user registrations'
  }),
  
  paymentProcessed: new promClient.Counter({
    name: 'payments_processed_total',
    help: 'Total number of payments processed',
    labelNames: ['method', 'status']
  })
};

// Register all metrics
register.registerMetric(httpRequestDuration);
register.registerMetric(httpRequestTotal);
register.registerMetric(activeConnections);
register.registerMetric(dbQueryDuration);
register.registerMetric(cacheHits);
register.registerMetric(cacheMisses);
register.registerMetric(queueSize);

Object.values(businessMetrics).forEach(metric => {
  register.registerMetric(metric);
});

// Metric collection helpers
const metrics = {
  recordHttpRequest: (method, route, statusCode, duration) => {
    httpRequestDuration.observe(
      { method, route, status_code: statusCode },
      duration / 1000
    );
    httpRequestTotal.inc({ method, route, status_code: statusCode });
  },

  recordDbQuery: (operation, table, duration) => {
    dbQueryDuration.observe({ operation, table }, duration / 1000);
  },

  recordCacheHit: (operation) => {
    cacheHits.inc({ operation });
  },

  recordCacheMiss: (operation) => {
    cacheMisses.inc({ operation });
  },

  setActiveConnections: (count) => {
    activeConnections.set(count);
  },

  setQueueSize: (queue, size) => {
    queueSize.set({ queue }, size);
  },

  recordBusinessMetric: (metric, value, labels = {}) => {
    if (businessMetrics[metric]) {
      if (businessMetrics[metric] instanceof promClient.Counter) {
        businessMetrics[metric].inc(labels);
      } else if (businessMetrics[metric] instanceof promClient.Histogram) {
        businessMetrics[metric].observe(labels, value);
      }
    }
  }
};

// Prometheus plugin for Fastify
const metricsPlugin = async (fastify, options) => {
  fastify.get('/metrics', async (request, reply) => {
    reply
      .header('Content-Type', register.contentType)
      .send(await register.metrics());
  });

  // Record metrics for each request
  fastify.addHook('onResponse', async (request, reply) => {
    const duration = reply.getResponseTime();
    metrics.recordHttpRequest(
      request.method,
      request.routerPath || request.url,
      reply.statusCode,
      duration
    );
  });
};

const setupPrometheus = () => {
  logger.info('Prometheus metrics initialized');
  
  // Update active connections every 10 seconds
  setInterval(() => {
    // This would get actual connection count
    metrics.setActiveConnections(Math.floor(Math.random() * 100));
  }, 10000);
};

module.exports = {
  register,
  metrics,
  metricsPlugin,
  setupPrometheus
};