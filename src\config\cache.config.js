const Redis = require('ioredis');
const { logger } = require('../utils/logger');

let redis = null;
let isClusterMode = false;

const initializeCache = async () => {
  try {
    const nodes = process.env.REDIS_CLUSTER_NODES?.split(',') || ['localhost:6379'];
    isClusterMode = nodes.length > 1;

    if (isClusterMode) {
      // Redis Cluster configuration
      redis = new Redis.Cluster(
        nodes.map(node => {
          const [host, port] = node.split(':');
          return { host, port: parseInt(port) };
        }),
        {
          redisOptions: {
            password: process.env.REDIS_PASSWORD,
            connectTimeout: 10000,
            maxRetriesPerRequest: 3,
          },
          clusterRetryStrategy: (times) => {
            const delay = Math.min(times * 50, 2000);
            return delay;
          },
          enableOfflineQueue: true,
        }
      );
      logger.info('✅ Redis cluster connected');
    } else {
      // Single Redis client configuration
      const [host, port] = nodes[0].split(':');
      redis = new Redis({
        host,
        port: parseInt(port),
        password: process.env.REDIS_PASSWORD,
        connectTimeout: 10000,
        retryStrategy: (times) => {
          const delay = Math.min(times * 50, 2000);
          return delay;
        },
        enableOfflineQueue: true,
      });
      logger.info('✅ Redis client connected');
    }

    // Handle connection events
    redis.on('error', (err) => {
      logger.error(`Redis ${isClusterMode ? 'cluster' : 'client'} error:`, err);
    });

    redis.on('connect', () => {
      logger.info(`Redis ${isClusterMode ? 'cluster' : 'client'} reconnected`);
    });

  } catch (error) {
    logger.error('❌ Cache initialization failed:', error);
    throw error;
  }
};

const getRedis = () => redis;

const cache = {
  async get(key) {
    try {
      const value = await redis.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error(`Cache get error for key ${key}:`, error);
      return null;
    }
  },

  async set(key, value, ttl = 3600) {
    try {
      const serialized = JSON.stringify(value);
      if (ttl) {
        await redis.set(key, serialized, 'EX', ttl);
      } else {
        await redis.set(key, serialized);
      }
      return true;
    } catch (error) {
      logger.error(`Cache set error for key ${key}:`, error);
      return false;
    }
  },

  async delete(key) {
    try {
      await redis.del(key);
      return true;
    } catch (error) {
      logger.error(`Cache delete error for key ${key}:`, error);
      return false;
    }
  },

  async deletePattern(pattern) {
    try {
      if (isClusterMode) {
        // In cluster mode, use keys command (ioredis handles cluster key distribution)
        const keys = [];
        const nodes = redis.nodes('master');
        for (const node of nodes) {
          const nodeKeys = await node.keys(pattern);
          keys.push(...nodeKeys);
        }
        if (keys.length > 0) {
          await redis.del(keys);
        }
      } else {
        // In single instance, use SCAN to avoid blocking
        let cursor = '0';
        do {
          const reply = await redis.scan(cursor, 'MATCH', pattern, 'COUNT', 100);
          cursor = reply[0];
          const foundKeys = reply[1];
          if (foundKeys.length > 0) {
            await redis.del(foundKeys);
          }
        } while (cursor !== '0');
      }
      return true;
    } catch (error) {
      logger.error(`Cache delete pattern error for ${pattern}:`, error);
      return false;
    }
  },

  async flush() {
    try {
      await redis.flushall();
      return true;
    } catch (error) {
      logger.error('Cache flush error:', error);
      return false;
    }
  },
};

module.exports = {
  initializeCache,
  getRedis,
  cache,
};