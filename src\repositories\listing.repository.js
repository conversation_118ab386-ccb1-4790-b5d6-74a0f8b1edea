const { Op } = require('sequelize');
const { getModels } = require('../models');
const { getDatabase } = require('../config/database.config');

const createListingRepository = ({ models }) => {
  console.log('🔍 Creating listing repository with models:', models);

  if (!models || !models.Listing) {
    console.error('❌ Listing model not found in models:', models);
    throw new Error('Listing model not available');
  }

  const getModelsForOperation = (operation = 'read') => {
    return getModels(operation);
  };

  const transaction = async () => {
    const db = getDatabase('write');
    return await db.transaction();
  };

  const repository = {
    async create(listingData, options = {}) {
      const models = getModelsForOperation('write');
      return await models.Listing.create(listingData, options);
    },

    transaction,

    async findById(id, includeAssociations = true) {
      const models = getModelsForOperation('read');
      const options = {
        where: { ListingID: id }
      };

      if (includeAssociations) {
        options.include = [
          // Temporarily commented out User association
          // {
          //   model: models.User,
          //   as: 'Lister',
          //   attributes: ['UserID', 'FullName', 'ProfileImageURL']
          // },
          {
            model: models.Category,
            as: 'Category',
            attributes: ['CategoryID', 'Name', 'IconURL']
          },
          {
            model: models.Subcategory,
            as: 'Subcategory',
            attributes: ['SubcategoryID', 'Name', 'IconURL']
          }
        ];
      }

      return await models.Listing.findOne(options);
    },

    async findAll(options = {}) {
      const models = getModelsForOperation('read');
      const {
        page = 1,
        limit = 20,
        categoryId,
        subcategoryId,
        location,
        minPrice,
        maxPrice,
        condition,
        isAvailable = true,
        isActive = true,
        sortBy = 'CreatedAt',
        sortOrder = 'DESC',
        search,
        featured = false
      } = options;

      const offset = (page - 1) * limit;
      const whereClause = {
        IsActive: isActive,
        IsAvailable: isAvailable
      };

      // Category filter
      if (categoryId) {
        whereClause.CategoryID = categoryId;
      }

      // Subcategory filter
      if (subcategoryId) {
        whereClause.SubcategoryID = subcategoryId;
      }

      // Location filter
      if (location) {
        whereClause.Location = {
          [Op.iLike]: `%${location}%`
        };
      }

      // Price range filter
      if (minPrice || maxPrice) {
        whereClause.PricePerDay = {};
        if (minPrice) whereClause.PricePerDay[Op.gte] = minPrice;
        if (maxPrice) whereClause.PricePerDay[Op.lte] = maxPrice;
      }

      // Condition filter
      if (condition) {
        whereClause.Condition = condition;
      }

      // Search filter
      if (search) {
        whereClause[Op.or] = [
          { Title: { [Op.iLike]: `%${search}%` } },
          { Description: { [Op.iLike]: `%${search}%` } }
        ];
      }

      // Featured filter
      if (featured) {
        whereClause.FeaturedUntil = {
          [Op.gte]: new Date()
        };
      }

      const queryOptions = {
        where: whereClause,
        include: [
          // Temporarily commented out User association
          // {
          //   model: models.User,
          //   as: 'Lister',
          //   attributes: ['UserID', 'FullName', 'ProfileImageURL']
          // },
          {
            model: models.Category,
            as: 'Category',
            attributes: ['CategoryID', 'Name', 'IconURL']
          },
          {
            model: models.Subcategory,
            as: 'Subcategory',
            attributes: ['SubcategoryID', 'Name', 'IconURL']
          }
        ],
        order: [[sortBy, sortOrder]],
        limit: parseInt(limit),
        offset: parseInt(offset)
      };

      const { count, rows } = await models.Listing.findAndCountAll(queryOptions);

      return {
        listings: rows,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit)
        }
      };
    },

    async findByCategory(categoryId, options = {}) {
      return await repository.findAll({ ...options, categoryId });
    },

    async findBySubcategory(subcategoryId, options = {}) {
      return await repository.findAll({ ...options, subcategoryId });
    },

    async findRelated(listingId, limit = 6) {
      const models = getModelsForOperation('read');

      // First get the current listing to find related items
      const currentListing = await repository.findById(listingId, false);
      if (!currentListing) return [];

      const whereClause = {
        ListingID: { [Op.ne]: listingId },
        IsActive: true,
        IsAvailable: true,
        [Op.or]: [
          { CategoryID: currentListing.CategoryID },
          { SubcategoryID: currentListing.SubcategoryID }
        ]
      };

      // If subcategory exists, prioritize same subcategory
      if (currentListing.SubcategoryID) {
        whereClause[Op.or] = [
          { SubcategoryID: currentListing.SubcategoryID },
          { CategoryID: currentListing.CategoryID }
        ];
      }

      return await models.Listing.findAll({
        where: whereClause,
        include: [
          // Temporarily commented out User association
          // {
          //   model: models.User,
          //   as: 'Lister',
          //   attributes: ['UserID', 'FullName', 'ProfileImageURL']
          // },
          {
            model: models.Category,
            as: 'Category',
            attributes: ['CategoryID', 'Name', 'IconURL']
          },
          {
            model: models.Subcategory,
            as: 'Subcategory',
            attributes: ['SubcategoryID', 'Name', 'IconURL']
          }
        ],
        order: [
          ['ViewCount', 'DESC'],
          ['CreatedAt', 'DESC']
        ],
        limit: parseInt(limit)
      });
    },

    async update(id, updateData) {
      const models = getModelsForOperation('write');
      const [updatedRowsCount] = await models.Listing.update(updateData, {
        where: { ListingID: id }
      });

      if (updatedRowsCount === 0) {
        return null;
      }

      return await repository.findById(id);
    },

    async delete(id) {
      const models = getModelsForOperation('write');
      return await models.Listing.destroy({
        where: { ListingID: id }
      });
    },

    async incrementViewCount(id) {
      const models = getModelsForOperation('write');
      return await models.Listing.increment('ViewCount', {
        where: { ListingID: id }
      });
    },

    async findByUser(userId, options = {}) {
      const { page = 1, limit = 20 } = options;
      const offset = (page - 1) * limit;

      const models = getModelsForOperation('read');

      const { count, rows } = await models.Listing.findAndCountAll({
        where: { ListerID: userId },
        include: [
          {
            model: models.Category,
            as: 'Category',
            attributes: ['CategoryID', 'Name', 'IconURL']
          },
          {
            model: models.Subcategory,
            as: 'Subcategory',
            attributes: ['SubcategoryID', 'Name', 'IconURL']
          }
        ],
        order: [['CreatedAt', 'DESC']],
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      return {
        listings: rows,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit)
        }
      };
    }
  };

  return repository;
};

module.exports = { createListingRepository };