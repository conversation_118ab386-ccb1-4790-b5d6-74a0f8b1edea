const ListingRepository = require('../repositories/listing.repository');
const CategoryRepository = require('../repositories/category.repository');
const { logger } = require('../utils/logger');

class ListingService {
  constructor() {
    this.listingRepository = new ListingRepository();
    this.categoryRepository = new CategoryRepository();
  }

  async createListing(listingData, userId) {
    try {
      console.log('Creating listing with userId:', userId);

      if (!userId) {
        throw new Error('User ID is required');
      }

      // Validate category exists
      const category = await this.categoryRepository.findById(listingData.CategoryID, false);
      if (!category) {
        throw new Error('Category not found');
      }

      // Extract specialized details and listing type
      const { ListingType, PropertyDetails, VehicleDetails, FurnitureDetails,
        HomeApplianceDetails, PhotographyEquipmentDetails, ...basicListingData } = listingData;

      // Add lister ID
      basicListingData.ListerID = userId;
      console.log('Basic listing data:', basicListingData);

      // Start a transaction
      const t = await this.listingRepository.transaction();

      try {
        // Create main listing
        const listing = await this.listingRepository.create(basicListingData, { transaction: t });

        // Get the specialized details based on listing type
        const specializedDetails = listingData[`${ListingType}Details`];

        const models = this.listingRepository.getModels('write');

        // Create specialized details based on listing type
        switch (ListingType) {
          case 'Property':
            await models.Property.create({
              ...specializedDetails,
              ListingID: listing.ListingID
            }, { transaction: t });
            break;
          
          case 'Vehicle':
            await models.Vehicle.create({
              ...specializedDetails,
              ListingID: listing.ListingID
            }, { transaction: t });
            break;
          
          case 'Furniture':
            await models.Furniture.create({
              ...specializedDetails,
              ListingID: listing.ListingID
            }, { transaction: t });
            break;
          
          case 'HomeAppliance':
            await models.HomeAppliance.create({
              ...specializedDetails,
              ListingID: listing.ListingID
            }, { transaction: t });
            break;
          
          case 'PhotographyEquipment':
            await models.PhotographyEquipment.create({
              ...specializedDetails,
              ListingID: listing.ListingID
            }, { transaction: t });
            break;          default:
            throw new Error('Invalid listing type');
        }

        // Commit transaction
        await t.commit();

        // Return complete listing with specialized details
        const result = await this.listingRepository.findById(listing.ListingID);
        
        // Add the specialized details to the response
        const specializedModel = models[ListingType];
        if (specializedModel) {
          const details = await specializedModel.findOne({
            where: { ListingID: listing.ListingID }
          });
          result.dataValues[`${ListingType}Details`] = details;
        }

        return result;
      } catch (error) {
        // Rollback transaction on error
        if (t) await t.rollback();
        throw error;
      }
    } catch (error) {
      logger.error('Error creating listing:', error);
      throw error;
    }
  }

  async getListingById(id, incrementView = false) {
    try {
      const listing = await this.listingRepository.findById(id);

      if (!listing) {
        throw new Error('Listing not found');
      }

      // Increment view count if requested
      // NOTE: For unique view tracking, use the new POST /api/v1/listings/:id/view endpoint
      // This method provides simple view counting for backward compatibility
      if (incrementView) {
        await this.listingRepository.incrementViewCount(id);
        listing.ViewCount += 1;
      }

      return listing;
    } catch (error) {
      logger.error('Error getting listing by ID:', error);
      throw error;
    }
  }

  async getAllListings(options = {}) {
    try {
      return await this.listingRepository.findAll(options);
    } catch (error) {
      logger.error('Error getting all listings:', error);
      throw error;
    }
  }

  async getListingsByCategory(categoryId, options = {}) {
    try {
      // Validate category exists
      const category = await this.categoryRepository.findById(categoryId, false);
      if (!category) {
        throw new Error('Category not found');
      }

      return await this.listingRepository.findByCategory(categoryId, options);
    } catch (error) {
      logger.error('Error getting listings by category:', error);
      throw error;
    }
  }

  async getListingsBySubcategory(subcategoryId, options = {}) {
    try {
      // Validate subcategory exists
      const subcategory = await this.categoryRepository.findSubcategoryById(subcategoryId);
      if (!subcategory) {
        throw new Error('Subcategory not found');
      }

      return await this.listingRepository.findBySubcategory(subcategoryId, options);
    } catch (error) {
      logger.error('Error getting listings by subcategory:', error);
      throw error;
    }
  }

  async searchListings(searchOptions = {}) {
    try {
      const {
        search,
        categoryId,
        subcategoryId,
        location,
        minPrice,
        maxPrice,
        condition,
        sortBy = 'CreatedAt',
        sortOrder = 'DESC',
        page = 1,
        limit = 20
      } = searchOptions;

      const options = {
        search,
        categoryId,
        subcategoryId,
        location,
        minPrice,
        maxPrice,
        condition,
        sortBy,
        sortOrder,
        page,
        limit
      };

      return await this.listingRepository.findAll(options);
    } catch (error) {
      logger.error('Error searching listings:', error);
      throw error;
    }
  }

  async getRelatedListings(listingId, limit = 6) {
    try {
      return await this.listingRepository.findRelated(listingId, limit);
    } catch (error) {
      logger.error('Error getting related listings:', error);
      throw error;
    }
  }

  async getFeaturedListings(options = {}) {
    try {
      return await this.listingRepository.findAll({
        ...options,
        featured: true,
        sortBy: 'FeaturedUntil',
        sortOrder: 'DESC'
      });
    } catch (error) {
      logger.error('Error getting featured listings:', error);
      throw error;
    }
  }

  async updateListing(id, updateData, userId) {
    try {
      // Check if listing exists and belongs to user
      const existingListing = await this.listingRepository.findById(id, false);
      if (!existingListing) {
        throw new Error('Listing not found');
      }

      if (existingListing.ListerID !== userId) {
        throw new Error('Unauthorized to update this listing');
      }

      // Validate category if being updated
      if (updateData.CategoryID) {
        const category = await this.categoryRepository.findById(updateData.CategoryID, false);
        if (!category) {
          throw new Error('Category not found');
        }
      }

      // Validate subcategory if being updated
      if (updateData.SubcategoryID) {
        const subcategory = await this.categoryRepository.findSubcategoryById(updateData.SubcategoryID);
        if (!subcategory) {
          throw new Error('Subcategory not found');
        }

        const categoryId = updateData.CategoryID || existingListing.CategoryID;
        if (subcategory.CategoryID !== categoryId) {
          throw new Error('Invalid subcategory for the selected category');
        }
      }

      return await this.listingRepository.update(id, updateData);
    } catch (error) {
      logger.error('Error updating listing:', error);
      throw error;
    }
  }

  async deleteListing(id, userId) {
    try {
      // Check if listing exists and belongs to user
      const existingListing = await this.listingRepository.findById(id, false);
      if (!existingListing) {
        throw new Error('Listing not found');
      }

      if (existingListing.ListerID !== userId) {
        throw new Error('Unauthorized to delete this listing');
      }

      return await this.listingRepository.delete(id);
    } catch (error) {
      logger.error('Error deleting listing:', error);
      throw error;
    }
  }

  async getUserListings(userId, options = {}) {
    try {
      return await this.listingRepository.findByUser(userId, options);
    } catch (error) {
      logger.error('Error getting user listings:', error);
      throw error;
    }
  }

  async toggleListingAvailability(id, userId) {
    try {
      const listing = await this.listingRepository.findById(id, false);
      if (!listing) {
        throw new Error('Listing not found');
      }

      if (listing.ListerID !== userId) {
        throw new Error('Unauthorized to modify this listing');
      }

      return await this.listingRepository.update(id, {
        IsAvailable: !listing.IsAvailable
      });
    } catch (error) {
      logger.error('Error toggling listing availability:', error);
      throw error;
    }
  }

  // Category management methods
  async getAllCategories() {
    try {
      return await this.categoryRepository.findAll();
    } catch (error) {
      logger.error('Error getting categories:', error);
      throw error;
    }
  }

  async getCategoryById(id) {
    try {
      const category = await this.categoryRepository.findById(id);
      if (!category) {
        throw new Error('Category not found');
      }
      return category;
    } catch (error) {
      logger.error('Error getting category by ID:', error);
      throw error;
    }
  }

  async getSubcategoriesByCategory(categoryId) {
    try {
      const category = await this.categoryRepository.findById(categoryId, false);
      if (!category) {
        throw new Error('Category not found');
      }

      return await this.categoryRepository.findSubcategoriesByCategory(categoryId);
    } catch (error) {
      logger.error('Error getting subcategories:', error);
      throw error;
    }
  }

  // Home page API - Get categories with sample data from each category
  async getHomePageData(options = {}) {
    try {
      const { limit = 8 } = options; // Limit items per category for home page

      // Get all categories with subcategories
      const categories = await this.categoryRepository.findAll(true);

      // For each category, get sample listings
      const categoriesWithData = await Promise.all(
        categories.map(async (category) => {
          const categoryListings = await this.listingRepository.findByCategory(
            category.CategoryID,
            { limit, page: 1 }
          );

          return {
            ...category.toJSON(),
            listings: categoryListings.listings || [],
            totalCount: categoryListings.pagination?.totalItems || 0
          };
        })
      );

      return {
        categories: categoriesWithData
      };
    } catch (error) {
      logger.error('Error getting home page data:', error);
      throw error;
    }
  }

  // Category page API - Get category with subcategories and all category data
  async getCategoryPageData(categoryId, options = {}) {
    try {
      const { subcategoryId, page = 1, limit = 20 } = options;

      // Get category with subcategories
      const category = await this.categoryRepository.findById(categoryId, true);
      if (!category) {
        throw new Error('Category not found');
      }

      // Get listings - either all category listings or filtered by subcategory
      let listingsResult;
      if (subcategoryId) {
        // Get listings for specific subcategory
        listingsResult = await this.listingRepository.findBySubcategory(subcategoryId, { page, limit });
      } else {
        // Get all listings for the category
        listingsResult = await this.listingRepository.findByCategory(categoryId, { page, limit });
      }

      return {
        category: {
          ...category.toJSON(),
          subcategories: category.Subcategories || []
        },
        listings: listingsResult.listings || [],
        pagination: listingsResult.pagination || {},
        selectedSubcategory: subcategoryId || null
      };
    } catch (error) {
      logger.error('Error getting category page data:', error);
      throw error;
    }
  }
}

module.exports = ListingService;
