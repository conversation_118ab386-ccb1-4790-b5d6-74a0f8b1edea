// test-grpc-client.js
const grpc = require('@grpc/grpc-js');
const protoLoader = require('@grpc/proto-loader');
const path = require('path');

const PROTO_PATH = path.join(__dirname, 'src/edge/grpc/protos/service.proto');

// Load proto file
const packageDefinition = protoLoader.loadSync(PROTO_PATH, {
  keepCase: true,
  longs: String,
  enums: String,
  defaults: true,
  oneofs: true
});

const proto = grpc.loadPackageDefinition(packageDefinition);

// Create clients
const userClient = new proto.app.UserService('localhost:50051', grpc.credentials.createInsecure());
const orderClient = new proto.app.OrderService('localhost:50051', grpc.credentials.createInsecure());
const authClient = new proto.app.AuthService('localhost:50051', grpc.credentials.createInsecure());

// Test functions
async function testUserService() {
  console.log('\n🧪 Testing User Service...');

  try {
    // Test ListUsers
    console.log('📋 Testing ListUsers...');
    const listUsersPromise = new Promise((resolve, reject) => {
      userClient.ListUsers({ page: 1, limit: 10 }, (error, response) => {
        if (error) reject(error);
        else resolve(response);
      });
    });

    const users = await listUsersPromise;
    console.log('✅ ListUsers successful:', JSON.stringify(users, null, 2));

    // Test CreateUser
    console.log('👤 Testing CreateUser...');
    const createUserPromise = new Promise((resolve, reject) => {
      authClient.Register({
        email: '<EMAIL>',
        username: 'testuser1',
        password: 't#stPass123',
        firstName: 'Test',
        lastName: 'User'
      }, (error, response) => {
        if (error) reject(error);
        else resolve(response);
      });
    });

    const newUser = await createUserPromise;
    console.log('✅ CreateUser successful:', JSON.stringify(newUser, null, 2));

    // Test GetUser
    if (newUser.data && newUser.data.id) {
      console.log('🔍 Testing GetUser...');
      const getUserPromise = new Promise((resolve, reject) => {
        userClient.GetUser({ userId: newUser.data.id }, (error, response) => {
          if (error) reject(error);
          else resolve(response);
        });
      });

      const user = await getUserPromise;
      console.log('✅ GetUser successful:', JSON.stringify(user, null, 2));
    }

  } catch (error) {
    console.error('❌ User service test failed:', error.message);
  }
}

function checkDeleteOrder(orderToDelete) {

  console.log('checking delete order');

  orderClient.CancelOrder({ orderId: orderToDelete, userId: '6a7c9f42-e029-41fa-873b-83627c1a2bf5', reason: 'My life my rules' }, (error, response) => {
    if (error) {
      console.error("Failed to cancel the order", error);
    }
    else console.log(JSON.stringify(response, null, 2));
  })
}

async function testOrderService() {
  console.log('\n🛍️  Testing Order Service...');

  try {
    // Test CreateOrder
    console.log('📦 Testing CreateOrder...');
    const createOrderPromise = new Promise((resolve, reject) => {
      orderClient.CreateOrder({
        userId: '6a7c9f42-e029-41fa-873b-83627c1a2bf5',
        items: [
          {
            productId: '6a7c9f42-e029-41fa-873b-83629c1a3bf5',
            name: 'Test Product',
            price: '29.99',
            quantity: 2
          }
        ],
        shippingAddress: {
          fullName: 'Test User',
          street: '123 Test St',
          city: 'Test City',
          state: 'TS',
          country: 'Test Country',
          postalCode: '12345',
          phone: '+1234567890'
        },
        paymentMethod: 'credit_card'
      }, (error, response) => {
        if (error) reject(error);
        else resolve(response);
      });
    });

    const newOrder = await createOrderPromise;
    console.log('✅ CreateOrder successful:', JSON.stringify(newOrder, null, 2));

    let orderToDelete;
    // Test GetOrder
    if (newOrder.order && newOrder.order.id) {
      console.log('🔍 Testing GetOrder...');
      const getOrderPromise = new Promise((resolve, reject) => {
        orderClient.GetOrder({ orderId: newOrder.order.id }, (error, response) => {
          if (error) reject(error);
          else resolve(response);
        });
      });

      const order = await getOrderPromise;
      orderToDelete = order.order.id;
      console.log('✅ GetOrder successful:', JSON.stringify(order, null, 2));
    }
    // Test StreamOrders
    console.log('📡 Testing StreamOrders...');
    const stream = orderClient.StreamOrders({ userId: '6a7c9f42-e029-41fa-873b-83627c1a2bf5' });

    stream.on('data', (response) => {
      console.log('📦 Received order stream:', JSON.stringify(response, null, 2));
    });

    stream.on('end', () => {
      console.log('✅ StreamOrders completed');
      checkDeleteOrder(orderToDelete)
    });

    stream.on('error', (error) => {
      console.error('❌ StreamOrders error:', error.message);
    });

  } catch (error) {
    console.error('❌ Order service test failed:', error.message);
  }
}

async function testReflection() {
  console.log('\n🔍 Testing gRPC Reflection...');

  // Create reflection client
  const reflectionClient = new grpc.Client('localhost:50051', grpc.credentials.createInsecure());

  try {
    // Test if reflection is available by making a simple call
    const testCall = reflectionClient.makeUnaryRequest(
      '/grpc.reflection.v1alpha.ServerReflection/ServerReflectionInfo',
      (arg) => arg,
      (arg) => arg,
      Buffer.from('test'),
      (error, response) => {
        if (error) {
          console.log('❌ Reflection not available:', error.message);
        } else {
          console.log('✅ Reflection is working');
        }
      }
    );
  } catch (error) {
    console.log('❌ Reflection test failed:', error.message);
  }
}

// Service discovery helper
function discoverServices() {
  console.log('\n🗺️  Service Discovery Information:');
  console.log('Available Services:');
  console.log('  - app.UserService');
  console.log('    Methods: GetUser, CreateUser, ListUsers');
  console.log('  - app.OrderService');
  console.log('    Methods: GetOrder, CreateOrder, StreamOrders, OrderUpdates');
  console.log('\nServer: localhost:50051');
  console.log('Proto file: src/edge/grpc/protos/service.proto');
}

// Main test function
async function runTests() {
  console.log('🚀 Starting gRPC Service Tests...');

  // Show service discovery info
  discoverServices();

  // Test reflection
  await testReflection();

  // Test services
  await testUserService();
  await testOrderService();

  console.log('\n✅ All tests completed!');

  // Close clients
  userClient.close();
  orderClient.close();
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testUserService,
  testOrderService,
  testReflection,
  discoverServices
};