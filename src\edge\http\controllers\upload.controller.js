const { logger } = require('../../../utils/logger');

class UploadController {
  // Upload images to Cloudinary
  async uploadImages(request, reply) {
    try {
      logger.info('📸 Starting image upload process...');

      // Lazy-load Cloudinary config to avoid startup crash if dependency is missing
      let uploadMultipleImages;
      try {
        ({ uploadMultipleImages } = require('../../../config/cloudinary.config'));
      } catch (e) {
        logger.error('Cloudinary dependency/config missing:', e.message);
        return reply.status(500).send({
          success: false,
          message: 'Image upload service is not configured on the server',
          error: 'Cloudinary module/config not available'
        });
      }

      // Check if multipart data is available
      if (!request.files) {
        logger.error('❌ Multipart plugin not properly registered');
        return reply.status(500).send({
          success: false,
          message: 'File upload service not available'
        });
      }

      const files = [];
      const parts = request.files();

      // Process each file part
      for await (const part of parts) {
        if (part.file) {
          // Validate file type
          if (!part.mimetype || !part.mimetype.startsWith('image/')) {
            logger.warn(`⚠️ Invalid file type: ${part.mimetype} for file: ${part.filename}`);
            continue; // Skip non-image files
          }

          const buffer = await part.toBuffer();

          // Validate file size (e.g., max 5MB)
          const maxSize = 5 * 1024 * 1024; // 5MB
          if (buffer.length > maxSize) {
            logger.warn(`⚠️ File too large: ${part.filename}, size: ${buffer.length} bytes`);
            continue; // Skip oversized files
          }

          files.push({
            buffer,
            filename: part.filename,
            mimetype: part.mimetype
          });

          logger.info(`📁 File received: ${part.filename}, size: ${buffer.length} bytes, type: ${part.mimetype}`);
        }
      }

      if (files.length === 0) {
        logger.warn('⚠️ No valid image files received in upload request');
        return reply.status(400).send({
          success: false,
          message: 'No valid image files uploaded. Please select image files (JPG, PNG, GIF, etc.) under 5MB.'
        });
      }

      logger.info(`🔄 Uploading ${files.length} files to Cloudinary...`);

      // Extract just the buffers for Cloudinary upload
      const fileBuffers = files.map(file => file.buffer);
      const results = await uploadMultipleImages(fileBuffers, 'rental-listings');

      logger.info(`✅ Successfully uploaded ${results.length} images to Cloudinary`);

      return reply.send({
        success: true,
        message: `Successfully uploaded ${results.length} images`,
        data: results
      });
    } catch (error) {
      logger.error('❌ Error uploading images:', error);
      return reply.status(500).send({
        success: false,
        message: 'Failed to upload images',
        error: error.message
      });
    }
  }
}

module.exports = UploadController;

