const { Op } = require('sequelize');
const { logger } = require('../utils/logger');
const { createBaseRepository } = require('./index');

const createChatRepository = ({ models }) => {
  console.log('🔍 Creating chat repository with models:', models);
  console.log('🔍 Available models:', Object.keys(models || {}));

  if (!models || !models.ChatMessage || !models.ChatConversation) {
    console.error('❌ Chat models not found in models:', models);
    throw new Error('Chat models not available');
  }

  const ChatConversation = models.ChatConversation;
  const ChatMessage = models.ChatMessage;
  const ChatConversationRead = models.ChatConversationRead;
  const ChatMessageRead = models.ChatMessageRead;
  const User = models.User;
  const UserRead = models.UserRead;
  const Listing = models.Listing;
  const ListingRead = models.ListingRead;

  console.log('✅ Chat models found:', ChatMessage.name, ChatConversation.name);

  const messageRepository = createBaseRepository(ChatMessage);
  const conversationRepository = createBaseRepository(ChatConversation);

  return {
    // Base repository methods for messages (only expose specific methods we need)
    createMessage: messageRepository.create,
    updateMessage: messageRepository.update,
    deleteMessage: messageRepository.delete,

    // Base repository methods for conversations (only expose specific methods we need)  
    createConversation: conversationRepository.create,
    updateConversation: conversationRepository.update,
    deleteConversation: conversationRepository.delete,

    // Create or get conversation
    async createOrGetConversation(user1Id, user2Id, listingId = null) {
      try {
        // Check if conversation already exists
        let conversation = await ChatConversation.findOne({
          where: {
            [Op.or]: [
              {
                user1Id: user1Id,
                user2Id: user2Id,
                listingId: listingId
              },
              {
                user1Id: user2Id,
                user2Id: user1Id,
                listingId: listingId
              }
            ]
          }
        });

        if (!conversation) {
          // Create new conversation
          const conversationId = `conv_${user1Id}_${user2Id}_${listingId || Date.now()}`;

          conversation = await ChatConversation.create({
            id: conversationId,
            user1Id,
            user2Id,
            listingId: listingId,
            isActive: true,
            lastActivity: new Date()
          });

          logger.info('New conversation created', {
            conversationId,
            user1Id,
            user2Id,
            listingId,
            listingType: listingId ? 'listing' : 'direct_message'
          });
        }

        return conversation;
      } catch (error) {
        logger.error('Error creating/getting conversation:', error);
        throw error;
      }
    },

    // Save message
    async saveMessage(messageData) {
      try {
        const message = await ChatMessage.create({
          id: messageData.id,
          conversationId: messageData.conversationId,
          senderId: messageData.senderId,
          recipientId: messageData.recipientId,
          content: messageData.content,
          type: messageData.type || 'text',
          isRead: messageData.isRead || false
        });

        // Update conversation last activity
        await ChatConversation.update({
          lastActivity: new Date()
        }, {
          where: {
            id: messageData.conversationId
          }
        });

        logger.info('Message saved', {
          messageId: message.id,
          conversationId: message.conversationId
        });

        return message;
      } catch (error) {
        logger.error('Error saving message:', error);
        throw error;
      }
    },

    // Update message status (mark as read)
    async updateMessageStatus(messageId, isRead = true, timestamp = null) {
      try {
        const updateData = { isRead };

        if (isRead && timestamp) {
          updateData.readAt = timestamp;
        }

        const [updatedRowsCount] = await ChatMessage.update(updateData, {
          where: { id: messageId }
        });

        logger.debug('Message status updated', {
          messageId,
          isRead,
          updatedRows: updatedRowsCount
        });

        return updatedRowsCount > 0;
      } catch (error) {
        logger.error('Error updating message status:', error);
        throw error;
      }
    },

    // Get user conversations
    async getUserConversations(userId, page = 1, limit = 20) {
      try {
        const offset = (page - 1) * limit;

        // Get basic conversation data without includes first
        const conversations = await ChatConversationRead.findAll({
          where: {
            [Op.or]: [
              { user1Id: userId },
              { user2Id: userId }
            ],
            isActive: true
          },
          order: [['lastActivity', 'DESC']],
          limit,
          offset
        });

        // Get additional data for each conversation
        const conversationsWithData = await Promise.all(
          conversations.map(async (conv) => {
            const [user1, user2, listingOwner, listing, unreadCount] = await Promise.all([
              conv.user1Id ? UserRead.findOne({
                where: { id: conv.user1Id },
                attributes: ['id', 'fullName', 'email', 'ProfileImageURL']
              }) : null,
              conv.user2Id ? UserRead.findOne({
                where: { id: conv.user2Id },
                attributes: ['id', 'fullName', 'email', 'ProfileImageURL']
              }) : null,
              conv.listingOwnerId ? UserRead.findOne({
                where: { id: conv.listingOwnerId },
                attributes: ['id', 'fullName', 'ProfileImageURL']
              }) : null,
              conv.listingId ? ListingRead.findOne({
                where: { ListingID: conv.listingId },
                attributes: ['ListingID', 'Title', 'PricePerDay', 'Location']
              }) : null,
              ChatMessageRead.count({
                where: {
                  conversationId: conv.id,
                  recipientId: userId,
                  isRead: false
                }
              })
            ]);

            // Determine other participant
            const otherParticipant = conv.user1Id === userId ? user2 : user1;

            return {
              id: conv.id,
              participants: [user1, user2].filter(Boolean),
              otherParticipant,
              listingInfo: listing,
              listingOwner,
              listingTitle: conv.listingTitle,
              listingImage: conv.listingImage,
              lastActivity: conv.lastActivity,
              unreadCount,
              createdAt: conv.createdAt
            };
          })
        );

        return conversationsWithData;
      } catch (error) {
        logger.error('Error getting user conversations:', error);
        throw error;
      }
    },

    // Get user conversations count
    async getUserConversationCount(userId) {
      try {
        const count = await ChatConversationRead.count({
          where: {
            [Op.or]: [
              { user1Id: userId },
              { user2Id: userId }
            ],
            isActive: true
          }
        });

        return count;
      } catch (error) {
        logger.error('Error getting user conversation count:', error);
        throw error;
      }
    },

    // Get conversation messages
    async getConversationMessages(conversationId, userId, page = 1, limit = 50) {
      try {
        const offset = (page - 1) * limit;

        // Verify user is participant
        const conversation = await ChatConversationRead.findOne({
          where: {
            id: conversationId,
            [Op.or]: [
              { user1Id: userId },
              { user2Id: userId }
            ]
          }
        });

        if (!conversation) {
          throw new Error('Conversation not found or access denied');
        }

        const messages = await ChatMessageRead.findAll({
          where: {
            conversationId,
            // Handle both possible field names for deleted messages
            ...(ChatMessageRead.rawAttributes.isDeleted
              ? { isDeleted: false }
              : {})
          },
          order: [['createdAt', 'ASC']],
          limit,
          offset
        });

        // Get user data separately for each message
        const messagesWithUserData = await Promise.all(
          messages.map(async (msg) => {
            const [sender, recipient] = await Promise.all([
              UserRead.findOne({
                where: { id: msg.senderId },
                attributes: ['id', 'fullName', 'ProfileImageURL']
              }),
              UserRead.findOne({
                where: { id: msg.recipientId },
                attributes: ['id', 'fullName', 'ProfileImageURL']
              })
            ]);

            return {
              id: msg.id,
              senderId: msg.senderId,
              recipientId: msg.recipientId,
              content: msg.content,
              type: msg.type,
              isRead: msg.isRead,
              timestamp: msg.createdAt,
              readAt: msg.readAt,
              sender,
              recipient
            };
          })
        );

        return {
          conversationId,
          messages: messagesWithUserData,
          totalMessages: await ChatMessageRead.count({
            where: {
              conversationId,
              ...(ChatMessageRead.rawAttributes.isDeleted
                ? { isDeleted: false }
                : {})
            }
          }),
          page,
          limit
        };
      } catch (error) {
        logger.error('Error getting conversation messages:', error);
        throw error;
      }
    },

    // Mark messages as read
    async markMessagesAsRead(conversationId, userId) {
      try {
        const [updatedRowsCount] = await ChatMessage.update({
          isRead: true,
          readAt: new Date()
        }, {
          where: {
            conversationId,
            recipientId: userId,
            isRead: false
          }
        });

        logger.debug('Messages marked as read', {
          conversationId,
          userId,
          updatedRows: updatedRowsCount
        });

        return updatedRowsCount;
      } catch (error) {
        logger.error('Error marking messages as read:', error);
        throw error;
      }
    },

    // Delete message (soft delete)
    async deleteMessage(messageId, userId) {
      try {
        const message = await ChatMessage.findOne({
          where: { id: messageId }
        });

        if (!message) {
          throw new Error('Message not found');
        }

        if (message.senderId !== userId) {
          throw new Error('Only sender can delete message');
        }

        // If the model has isDeleted field, use it, otherwise just delete
        if (ChatMessage.rawAttributes.isDeleted) {
          await ChatMessage.update({
            isDeleted: true
          }, {
            where: { id: messageId }
          });
        } else {
          await ChatMessage.destroy({
            where: { id: messageId }
          });
        }

        logger.info('Message deleted', { messageId, userId });

        return true;
      } catch (error) {
        logger.error('Error deleting message:', error);
        throw error;
      }
    },

    // Search messages
    async searchMessages(userId, query, limit = 20) {
      try {
        const messages = await ChatMessageRead.findAll({
          where: {
            [Op.or]: [
              { senderId: userId },
              { recipientId: userId }
            ],
            content: {
              [Op.iLike]: `%${query}%`
            },
            ...(ChatMessageRead.rawAttributes.isDeleted
              ? { isDeleted: false }
              : {})
          },
          order: [['createdAt', 'DESC']],
          limit
        });

        // Get related data separately
        const messagesWithData = await Promise.all(
          messages.map(async (msg) => {
            const [sender, conversation] = await Promise.all([
              UserRead.findOne({
                where: { id: msg.senderId },
                attributes: ['id', 'fullName', 'ProfileImageURL']
              }),
              ChatConversationRead.findOne({
                where: { id: msg.conversationId },
                attributes: ['id', 'user1Id', 'user2Id']
              })
            ]);

            return {
              ...msg.toJSON(),
              sender,
              conversation
            };
          })
        );

        return messagesWithData;
      } catch (error) {
        logger.error('Error searching messages:', error);
        throw error;
      }
    },

    // Get conversation by ID
    async getConversationById(conversationId) {
      try {
        // First get the basic conversation data
        const conversation = await ChatConversationRead.findOne({
          where: { id: conversationId }
        });

        if (!conversation) {
          return null;
        }

        // Then get the related user data separately to avoid association conflicts
        const [user1, user2, listingOwner, listing] = await Promise.all([
          conversation.user1Id ? UserRead.findOne({
            where: { id: conversation.user1Id },
            attributes: ['id', 'fullName', 'email', 'ProfileImageURL']
          }) : null,
          conversation.user2Id ? UserRead.findOne({
            where: { id: conversation.user2Id },
            attributes: ['id', 'fullName', 'email', 'ProfileImageURL']
          }) : null,
          conversation.listingOwnerId ? UserRead.findOne({
            where: { id: conversation.listingOwnerId },
            attributes: ['id', 'fullName', 'ProfileImageURL']
          }) : null,
          conversation.listingId ? ListingRead.findOne({
            where: { ListingID: conversation.listingId },
            attributes: ['ListingID', 'Title', 'PricePerDay', 'Location']
          }) : null
        ]);

        // Combine the data
        return {
          ...conversation.toJSON(),
          user1,
          user2,
          listingOwner,
          listing
        };
      } catch (error) {
        logger.error('Error getting conversation by ID:', error);
        throw error;
      }
    },

    // Get conversation message count
    async getConversationMessageCount(conversationId) {
      try {
        const count = await ChatMessageRead.count({
          where: {
            conversationId,
            ...(ChatMessageRead.rawAttributes.isDeleted
              ? { isDeleted: false }
              : {})
          }
        });

        return count;
      } catch (error) {
        logger.error('Error getting conversation message count:', error);
        throw error;
      }
    },

    // Mark messages as delivered
    async markMessagesAsDelivered(conversationId, userId) {
      try {
        // Since our current model doesn't have a delivered status, 
        // we'll just update the readAt field for now
        const [updatedRowsCount] = await ChatMessage.update({
          readAt: new Date()
        }, {
          where: {
            conversationId,
            recipientId: userId,
            readAt: null
          }
        });

        logger.debug('Messages marked as delivered', {
          conversationId,
          userId,
          updatedRows: updatedRowsCount
        });

        return updatedRowsCount;
      } catch (error) {
        logger.error('Error marking messages as delivered:', error);
        throw error;
      }
    },

    // Update last activity
    async updateLastActivity(conversationId) {
      try {
        const [updatedRowsCount] = await ChatConversation.update({
          lastActivity: new Date()
        }, {
          where: { id: conversationId }
        });

        logger.debug('Conversation last activity updated', {
          conversationId,
          updatedRows: updatedRowsCount
        });

        return updatedRowsCount > 0;
      } catch (error) {
        logger.error('Error updating conversation last activity:', error);
        throw error;
      }
    }
  };
};

module.exports = { createChatRepository };