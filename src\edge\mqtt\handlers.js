const { logger } = require('../../utils/logger');
const { publishToRabbit,  } = require('../../config/queue.config');
const { metrics } = require('../../monitoring/prometheus');

const createMqttHandlers = (container) => {
  return {
    async handleDeviceMessage(topic, payload) {
      try {
        const topicParts = topic.split('/');
        const deviceId = topicParts[1];
        const messageType = topicParts[2];
        const data = JSON.parse(payload);

        switch (messageType) {
          case 'telemetry':
            await this.processTelemetry(deviceId, data);
            break;

          case 'status':
            await this.processDeviceStatus(deviceId, data);
            break;

          case 'commands':
            // Commands are outbound only
            break;

          default:
            logger.warn(`Unknown device message type: ${messageType}`);
        }
      } catch (error) {
        logger.error('Device message handling error:', error);
      }
    },

    async processTelemetry(deviceId, data) {
      // Store telemetry data
      const { cache } = require('../../config/cache.config');
      const telemetryKey = `device:${deviceId}:telemetry`;
      
      await cache.set(telemetryKey, data, 300); // 5 minutes

      // Check for anomalies
      if (data.temperature > 50 || data.humidity > 90) {
        await this.createAlert('warning', {
          type: 'telemetry_anomaly',
          deviceId,
          data,
          message: 'Abnormal telemetry values detected'
        });
      }

      // Publish to analytics pipeline
      // await publishToKafka('device-telemetry', {
      //   deviceId,
      //   telemetry: data,
      //   timestamp: new Date()
      // });

      // Update metrics
      if (data.temperature) {
        metrics.recordBusinessMetric('deviceTemperature', data.temperature, { deviceId });
      }

      logger.debug(`Telemetry processed for device ${deviceId}`);
    },

    async processDeviceStatus(deviceId, data) {
      const { cache } = require('../../config/cache.config');
      const statusKey = `device:${deviceId}:status`;
      
      // Get previous status
      const previousStatus = await cache.get(statusKey);
      
      // Store new status
      await cache.set(statusKey, data, 3600); // 1 hour

      // Check for status changes
      if (previousStatus && previousStatus.online !== data.online) {
        await publishToRabbit('events', 'device.status-changed', {
          deviceId,
          previousStatus: previousStatus.online,
          currentStatus: data.online,
          timestamp: new Date()
        });

        if (!data.online) {
          await this.createAlert('info', {
            type: 'device_offline',
            deviceId,
            message: `Device ${deviceId} went offline`
          });
        }
      }

      logger.debug(`Status updated for device ${deviceId}: ${data.online ? 'online' : 'offline'}`);
    },

    async handleOrderMessage(topic, payload) {
      try {
        const topicParts = topic.split('/');
        const orderId = topicParts[1];
        const data = JSON.parse(payload);

        // Validate order exists
        const { orderService } = container;
        const order = await orderService.getOrderById(orderId);

        if (!order) {
          logger.warn(`Order not found for MQTT update: ${orderId}`);
          return;
        }

        // Process order update
        if (data.status) {
          await orderService.updateOrderStatus(orderId, data.status, {
            source: 'mqtt',
            ...data.metadata
          });
        }

        // Notify via WebSocket
        const { getRedisClient } = require('../../config/cache.config');
        const publisher = getRedisClient();

        await publisher.publish('socket:broadcast', JSON.stringify({
          type: 'order:status-changed',
          userId: order.userId,
          trackingNumber: order.trackingNumber,
          payload: {
            orderId: order.id,
            status: data.status,
            updatedAt: new Date()
          }
        }));

        logger.info(`Order ${orderId} updated via MQTT`);
      } catch (error) {
        logger.error('Order message handling error:', error);
      }
    },

    async handleAlertMessage(topic, payload) {
      try {
        const topicParts = topic.split('/');
        const alertLevel = topicParts[1];
        const data = JSON.parse(payload);

        // Store alert
        await this.storeAlert(alertLevel, data);

        // Process based on alert level
        switch (alertLevel) {
          case 'critical':
            await this.processCriticalAlert(data);
            break;
          case 'warning':
            await this.processWarningAlert(data);
            break;
          case 'info':
            await this.processInfoAlert(data);
            break;
        }

        logger.info(`Alert received: ${alertLevel}`, { type: data.type });
      } catch (error) {
        logger.error('Alert message handling error:', error);
      }
    },

    async handleHealthMessage(topic, payload) {
      try {
        const data = JSON.parse(payload);
        const { cache } = require('../../config/cache.config');

        // Store health data
        await cache.set(
          `health:${data.clientId}`,
          data,
          60 // 1 minute
        );

        // Check for unhealthy services
        if (data.status !== 'healthy') {
          await this.createAlert('warning', {
            type: 'service_unhealthy',
            service: data.clientId,
            status: data.status,
            message: `Service ${data.clientId} reported unhealthy status`
          });
        }

        logger.debug(`Health update from ${data.clientId}: ${data.status}`);
      } catch (error) {
        logger.error('Health message handling error:', error);
      }
    },

    async handleSystemMessage(topic, payload) {
      try {
        // Handle MQTT broker system messages
        if (topic === '$SYS/broker/clients/connected') {
          const connectedClients = parseInt(payload);
          metrics.setActiveConnections(connectedClients);
          logger.debug(`Connected MQTT clients: ${connectedClients}`);
        }
      } catch (error) {
        logger.error('System message handling error:', error);
      }
    },

    async createAlert(level, alertData) {
      const alert = {
        id: `alert_${Date.now()}`,
        level,
        ...alertData,
        createdAt: new Date()
      };

      // Store alert
      await this.storeAlert(level, alert);

      // Publish to notification system
      await publishToRabbit('events', 'alert.created', alert);

      return alert;
    },

    async storeAlert(level, alert) {
      const { cache } = require('../../config/cache.config');
      const key = `alerts:${level}:${alert.id || Date.now()}`;
      
      await cache.set(key, alert, 86400); // 24 hours

      // Add to alert list
      const listKey = `alerts:${level}:list`;
      const alertList = await cache.get(listKey) || [];
      alertList.unshift(alert);
      
      // Keep only last 100 alerts
      if (alertList.length > 100) {
        alertList.pop();
      }
      
      await cache.set(listKey, alertList, 86400);
    },

    async processCriticalAlert(alert) {
      // Send immediate notifications
      const { notificationService } = container;
      
      // Notify admins
      await notificationService.sendPushNotification(
        'admin-group',
        'Critical Alert',
        alert.message,
        { alertId: alert.id }
      );

      // Send SMS to on-call
      await notificationService.sendSMS(
        process.env.ON_CALL_PHONE,
        `CRITICAL: ${alert.message}`
      );

      // Create incident
      await publishToRabbit('events', 'incident.create', {
        severity: 'critical',
        alert,
        timestamp: new Date()
      });
    },

    async processWarningAlert(alert) {
      // Send notifications to relevant teams
      await publishToRabbit('events', 'notification.send', {
        type: 'warning-alert',
        recipients: ['ops-team'],
        data: alert
      });
    },

    async processInfoAlert(alert) {
      // Log and store for dashboard
      logger.info('Info alert:', alert);
    }
  };
};

module.exports = { createMqttHandlers };