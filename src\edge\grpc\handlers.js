const createGrpcHandlers = (container, jwt) => {
    return {

        async Login(call, callback) {
            try {
                // const { email, password } = call.request;
                const authMediator = container.resolve("authMediator");
                const result = await authMediator.login(call.request, jwt);
                // const userRepository = container.resolve('userRepository')
                // const userService = container.resolve('userService')

                // // Find user by email
                // const user = await userRepository.findByEmail(email);

                // if (!user) {
                //     throw new Error('Invalid credentials');
                // }

                // // Check if account is locked
                // if (user.isLocked()) {
                //     throw new Error('Account is locked. Please try again later');
                // }

                // // Verify password
                // const isValidPassword = await user.comparePassword(password);

                // if (!isValidPassword) {
                //     // Increment login attempts
                //     await user.incrementLoginAttempts();
                //     throw new Error('Invalid credentials');
                // }

                // // Check if account is active
                // if (user.status !== 'active') {
                //     throw new Error('Account is not active');
                // }

                // // Reset login attempts
                // await user.resetLoginAttempts();

                // // Update last login
                // await userService.updateLastLogin(user.id);

                // // Generate tokens
                // const accessToken = await jwt.sign({
                //     id: user.id,
                //     email: user.email,
                //     role: user.role
                // });

                // const refreshToken = uuidv4();

                // // Store refresh token (in production, store in database)
                // // console.log('this is the cache', container.resolve('cache'))
                // await container.resolve('cache').cache.set(
                //     `refresh_token:${refreshToken}`,
                //     { userId: user.id },
                //     30 * 24 * 60 * 60 // 30 days
                // );

                logger.info(`User logged in: ${user.id}`);

                callback(null, result);

            } catch (error) {
                logger.error(`gRPC login failed ${error}`);
                callback({
                    code: grpc.status.NOT_FOUND,
                    message: error.message
                });
            }
        },

        // User service handlers
        async GetUser(call, callback) {
            try {
                const { userId } = call.request;
                const userMediator = container.resolve('userMediator')
                // const { userService } = container;

                const returnedData = await userMediator.getUser(userId);
                callback(null, returnedData);

            } catch (error) {
                logger.error(`gRPC GetUser error ${error}`);
                callback({
                    code: grpc.status.NOT_FOUND,
                    message: error.message
                });
            }
        },

        async CreateUser(call, callback) {
            try {
                const data = call.request;

                const validator = container.resolve('userValidator');
                const userData = await validator.validate(data, 'userCreate');
                const userMediator = container.resolve('userMediator');

                const returnedData = await userMediator.createUser(userData);

                callback(null, returnedData);

            } catch (error) {
                logger.error(`gRPC CreateUser error ${error}`);
                callback({
                    code: grpc.status.INVALID_ARGUMENT,
                    message: error.message
                });
            }
        },

        async UpdateUser(call, callback) {
            try {
                const { userId } = call.request;
                if (!userId) throw new Error('Failed to update the user, id is required')

                const validator = container.resolve('userValidator')
                const userMediator = container.resolve('userMediator')

                const validatedData = await validator.validate(call.request, 'userUpdate');

                const updatedUser = await userMediator.updateUser(userId, validatedData)

                callback(null, { ...updatedUser })

            } catch (error) {
                logger.error(`gRPC UpdateUser error: ${error}`);
                callback({
                    code: grpc.status.INTERNAL,
                    message: error.message
                });
            }
        },
        async DeleteUser(call, callback) {
            try {
                const { userId } = call.request;

                const userMediator = container.resolve('userMediator')
                const returnedData = await userMediator.deleteUser(userId)

                callback(null, returnedData);

            } catch (error) {
                logger.error(`gRPC delete user error: ${error}`);
                callback({
                    code: grpc.status.INTERNAL,
                    message: error.message
                });
            }
        },

        async ListUsers(call, callback) {
            try {
                const { page = 1, limit = 20, status, role, search } = call.request;

                const filters = {};
                if (status) filters.status = status;
                if (role) filters.role = role;

                const metaData = { filters, page, limit, search }

                const userMediator = container.resolve('userMediator');

                const result = await userMediator.listUsers(metaData);

                callback(null, {
                    users: result.data.map(user => ({
                        id: user.id,
                        email: user.email,
                        username: user.username,
                        first_name: user.firstName,
                        last_name: user.lastName,
                        role: user.role,
                        status: user.status
                    })),
                    total: result.total,
                    page: result.page,
                    total_pages: result.totalPages
                });
            } catch (error) {
                logger.error(`gRPC ListUsers error: ${error}`);
                callback({
                    code: grpc.status.INTERNAL,
                    message: error.message
                });
            }
        },
    };
};

module.exports = createGrpcHandlers;